#!/usr/bin/env python3
"""
简化的操作代理模块

提供基本的设备操作功能，去掉复杂依赖
"""

import json
from typing import Dict, Any
from tools.screenshot_utils import screen_action


def action_agent(parsed_action: Dict[str, Any], device: str) -> Dict[str, Any]:
    """
    简化的操作代理，直接执行解析后的操作
    
    Args:
        parsed_action: 解析后的操作字典
        device: 设备ID
        
    Returns:
        操作结果字典
    """
    try:
        action_type = parsed_action.get("action", "").lower()
        
        if action_type == "tap" or action_type == "click":
            x = parsed_action.get("x")
            y = parsed_action.get("y")
            
            if x is not None and y is not None:
                result = screen_action(
                    device=device,
                    action="tap",
                    x=int(x),
                    y=int(y)
                )
                return {"status": "success", "result": result, "action": "tap"}
            else:
                return {"status": "error", "message": "Missing coordinates for tap action"}
        
        elif action_type == "type" or action_type == "input":
            content = parsed_action.get("content", "")
            
            if content:
                result = screen_action(
                    device=device,
                    action="text",
                    input_str=content
                )
                return {"status": "success", "result": result, "action": "type"}
            else:
                return {"status": "error", "message": "Missing content for type action"}
        
        elif action_type == "long_press":
            x = parsed_action.get("x")
            y = parsed_action.get("y")
            duration = parsed_action.get("duration", 1000)
            
            if x is not None and y is not None:
                result = screen_action(
                    device=device,
                    action="long_press",
                    x=int(x),
                    y=int(y),
                    duration=int(duration)
                )
                return {"status": "success", "result": result, "action": "long_press"}
            else:
                return {"status": "error", "message": "Missing coordinates for long_press action"}
        
        elif action_type == "swipe":
            direction = parsed_action.get("direction")
            x = parsed_action.get("x", 500)  # 默认中心位置
            y = parsed_action.get("y", 1000)
            dist = parsed_action.get("dist", "medium")
            quick = parsed_action.get("quick", False)
            
            if direction:
                result = screen_action(
                    device=device,
                    action="swipe",
                    x=int(x),
                    y=int(y),
                    direction=direction,
                    dist=dist,
                    quick=quick
                )
                return {"status": "success", "result": result, "action": "swipe"}
            else:
                return {"status": "error", "message": "Missing direction for swipe action"}
        
        elif action_type == "drag":
            start_box = parsed_action.get("start_box")
            end_box = parsed_action.get("end_box")
            
            if start_box and end_box:
                # 解析坐标
                start_coords = parse_box_coordinates(start_box)
                end_coords = parse_box_coordinates(end_box)
                
                if start_coords and end_coords:
                    result = screen_action(
                        device=device,
                        action="swipe_precise",
                        start=start_coords,
                        end=end_coords,
                        duration=400
                    )
                    return {"status": "success", "result": result, "action": "drag"}
                else:
                    return {"status": "error", "message": "Invalid coordinates for drag action"}
            else:
                return {"status": "error", "message": "Missing start_box or end_box for drag action"}
        
        elif action_type == "back":
            result = screen_action(
                device=device,
                action="back"
            )
            return {"status": "success", "result": result, "action": "back"}
        
        elif action_type == "wait":
            # 等待操作，直接返回成功
            return {"status": "success", "result": "wait completed", "action": "wait"}
        
        elif action_type == "finished":
            # 完成操作
            content = parsed_action.get("content", "Step completed")
            return {"status": "success", "result": content, "action": "finished"}
        
        else:
            return {"status": "error", "message": f"Unsupported action type: {action_type}"}
    
    except Exception as e:
        return {"status": "error", "message": f"Action execution failed: {str(e)}"}


def parse_box_coordinates(box_str: str) -> tuple:
    """
    解析坐标框字符串
    
    Args:
        box_str: 坐标框字符串，如 "<|box_start|>(100, 200)<|box_end|>"
        
    Returns:
        坐标元组 (x, y) 或 None
    """
    try:
        import re
        
        # 提取坐标
        pattern = r'<\|box_start\|\>\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)\s*<\|box_end\|\>'
        match = re.search(pattern, box_str)
        
        if match:
            x = int(match.group(1))
            y = int(match.group(2))
            return (x, y)
        
        # 尝试简单的坐标格式
        pattern2 = r'\(\s*(\d+)\s*,\s*(\d+)\s*\)'
        match2 = re.search(pattern2, box_str)
        
        if match2:
            x = int(match2.group(1))
            y = int(match2.group(2))
            return (x, y)
        
        return None
        
    except Exception:
        return None


def execute_simple_action(action_str: str, device: str) -> Dict[str, Any]:
    """
    执行简单的操作字符串
    
    Args:
        action_str: 操作字符串，如 "tap(100, 200)" 或 "type('hello')"
        device: 设备ID
        
    Returns:
        操作结果字典
    """
    try:
        import re
        
        # 解析tap操作
        tap_match = re.match(r'tap\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)', action_str)
        if tap_match:
            x, y = int(tap_match.group(1)), int(tap_match.group(2))
            return action_agent({"action": "tap", "x": x, "y": y}, device)
        
        # 解析click操作
        click_match = re.match(r'click\s*\(\s*start_box\s*=\s*[\'"]([^\'"]+)[\'"]\s*\)', action_str)
        if click_match:
            box_str = click_match.group(1)
            coords = parse_box_coordinates(box_str)
            if coords:
                return action_agent({"action": "tap", "x": coords[0], "y": coords[1]}, device)
        
        # 解析type操作
        type_match = re.match(r'type\s*\(\s*content\s*=\s*[\'"]([^\'"]+)[\'"]\s*\)', action_str)
        if type_match:
            content = type_match.group(1)
            return action_agent({"action": "type", "content": content}, device)
        
        # 解析long_press操作
        long_press_match = re.match(r'long_press\s*\(\s*x\s*=\s*(\d+)\s*,\s*y\s*=\s*(\d+)\s*\)', action_str)
        if long_press_match:
            x, y = int(long_press_match.group(1)), int(long_press_match.group(2))
            return action_agent({"action": "long_press", "x": x, "y": y}, device)
        
        # 解析drag操作
        drag_match = re.match(r'drag\s*\(\s*start_box\s*=\s*[\'"]([^\'"]+)[\'"]\s*,\s*end_box\s*=\s*[\'"]([^\'"]+)[\'"]\s*\)', action_str)
        if drag_match:
            start_box = drag_match.group(1)
            end_box = drag_match.group(2)
            return action_agent({"action": "drag", "start_box": start_box, "end_box": end_box}, device)
        
        # 解析wait操作
        if "wait()" in action_str:
            return action_agent({"action": "wait"}, device)
        
        # 解析finished操作
        finished_match = re.match(r'finished\s*\(\s*content\s*=\s*[\'"]([^\'"]+)[\'"]\s*\)', action_str)
        if finished_match:
            content = finished_match.group(1)
            return action_agent({"action": "finished", "content": content}, device)
        
        # 解析back操作
        if "back" in action_str.lower():
            return action_agent({"action": "back"}, device)
        
        return {"status": "error", "message": f"Cannot parse action string: {action_str}"}
        
    except Exception as e:
        return {"status": "error", "message": f"Failed to execute action string: {str(e)}"}


def validate_action(parsed_action: Dict[str, Any]) -> bool:
    """
    验证操作是否有效
    
    Args:
        parsed_action: 解析后的操作字典
        
    Returns:
        是否有效
    """
    if not isinstance(parsed_action, dict):
        return False
    
    action_type = parsed_action.get("action", "").lower()
    
    if action_type in ["tap", "click"]:
        return "x" in parsed_action and "y" in parsed_action
    elif action_type in ["type", "input"]:
        return "content" in parsed_action
    elif action_type == "long_press":
        return "x" in parsed_action and "y" in parsed_action
    elif action_type == "swipe":
        return "direction" in parsed_action
    elif action_type == "drag":
        return "start_box" in parsed_action and "end_box" in parsed_action
    elif action_type in ["back", "wait", "finished"]:
        return True
    
    return False
