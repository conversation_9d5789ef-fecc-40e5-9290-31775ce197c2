#!/usr/bin/env python3
"""
截图工具模块

提供简化的截图和设备操作功能，去掉无效依赖
"""

import os
import json
import subprocess
import datetime
from time import sleep
from typing import Dict, Any


def execute_adb(adb_command: str) -> str:
    """
    执行ADB命令
    
    Args:
        adb_command: ADB命令字符串
        
    Returns:
        命令执行结果或ERROR
    """
    try:
        result = subprocess.run(
            adb_command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=30  # 添加超时
        )
        if result.returncode == 0:
            return result.stdout.strip()
        print(f"Command execution failed: {adb_command}")
        print(result.stderr)
        return "ERROR"
    except subprocess.TimeoutExpired:
        print(f"Command timeout: {adb_command}")
        return "ERROR"
    except Exception as e:
        print(f"Command exception: {adb_command}, error: {str(e)}")
        return "ERROR"


def list_all_devices() -> list:
    """
    列出所有连接的设备
    
    Returns:
        设备ID列表
    """
    adb_command = "adb devices"
    device_list = []
    result = execute_adb(adb_command)
    if result != "ERROR":
        devices = result.split("\n")[1:]
        for d in devices:
            if d.strip() and "\tdevice" in d:
                device_list.append(d.split()[0])
    return device_list


def get_device_size(device: str = "emulator") -> Dict[str, Any]:
    """
    获取设备屏幕尺寸
    
    Args:
        device: 设备ID
        
    Returns:
        包含width和height的字典，或错误信息
    """
    adb_command = f"adb -s {device} shell wm size"
    result = execute_adb(adb_command)
    if result != "ERROR":
        try:
            size_str = result.split(": ")[1]
            width, height = map(int, size_str.split("x"))
            return {"width": width, "height": height}
        except (IndexError, ValueError) as e:
            return {"error": f"Failed to parse device size: {str(e)}"}
    return {"error": "Failed to get device size. Please check device connection or permissions."}


def take_screenshot(
    device: str = "emulator",
    save_dir: str = "./log/screenshots",
    app_name: str = None,
    step: int = 0,
) -> str:
    """
    截取设备屏幕截图
    
    Args:
        device: 设备ID
        save_dir: 保存目录
        app_name: 应用名称
        step: 步骤编号
        
    Returns:
        截图文件路径或错误信息
    """
    try:
        # 创建保存目录
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        if app_name is None:
            app_name = "unknown_app"

        # 创建应用子目录
        app_dir = os.path.join(save_dir, app_name)
        if not os.path.exists(app_dir):
            os.makedirs(app_dir)

        # 生成文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        if step is not None:
            filename = f"{app_name}_step{step}_{timestamp}.png"
        else:
            filename = f"{app_name}_{timestamp}.png"

        screenshot_file = os.path.join(app_dir, filename)
        
        # 使用简化的截图命令
        adb_command = f"adb -s {device} exec-out screencap -p > {screenshot_file}"
        
        # 等待设备稳定
        sleep(1)
        
        # 执行截图命令
        result = execute_adb(adb_command)
        
        # 检查文件是否成功创建
        if os.path.exists(screenshot_file) and os.path.getsize(screenshot_file) > 0:
            return screenshot_file
        else:
            # 尝试备用方法
            return take_screenshot_fallback(device, screenshot_file)
            
    except Exception as e:
        print(f"Screenshot failed, error: {str(e)}")
        return f"Screenshot failed, error information: {str(e)}"


def take_screenshot_fallback(device: str, screenshot_file: str) -> str:
    """
    备用截图方法
    
    Args:
        device: 设备ID
        screenshot_file: 截图文件路径
        
    Returns:
        截图文件路径或错误信息
    """
    try:
        # 备用方法：先保存到设备，再拉取
        remote_file = f"/sdcard/temp_screenshot_{datetime.datetime.now().strftime('%H%M%S')}.png"
        
        # 截图到设备
        cap_command = f"adb -s {device} shell screencap -p {remote_file}"
        if execute_adb(cap_command) != "ERROR":
            # 拉取到本地
            pull_command = f"adb -s {device} pull {remote_file} {screenshot_file}"
            if execute_adb(pull_command) != "ERROR":
                # 删除设备上的临时文件
                delete_command = f"adb -s {device} shell rm {remote_file}"
                execute_adb(delete_command)
                
                if os.path.exists(screenshot_file) and os.path.getsize(screenshot_file) > 0:
                    return screenshot_file
        
        return "Screenshot failed. Please check device connection or permissions."
        
    except Exception as e:
        return f"Fallback screenshot failed: {str(e)}"


def screen_action(
    device: str = "emulator",
    action: str = "tap",
    x: int = None,
    y: int = None,
    input_str: str = None,
    duration: int = 1000,
    direction: str = None,
    dist: str = "medium",
    quick: bool = False,
    start: tuple = None,
    end: tuple = None,
) -> str:
    """
    执行屏幕操作
    
    Args:
        device: 设备ID
        action: 操作类型 (tap, back, text, long_press, swipe, swipe_precise)
        x, y: 坐标
        input_str: 输入文本
        duration: 持续时间
        direction: 滑动方向
        dist: 滑动距离
        quick: 是否快速滑动
        start, end: 精确滑动的起始和结束点
        
    Returns:
        操作结果JSON字符串
    """
    try:
        adb_command = None
        result_data = {"action": action, "device": device}

        if action == "back":
            adb_command = f"adb -s {device} shell input keyevent KEYCODE_BACK"

        elif action == "tap":
            if x is None or y is None:
                return json.dumps({
                    "status": "error",
                    "action": action,
                    "device": device,
                    "message": "Missing required parameters for tap action (x, y)",
                })
            adb_command = f"adb -s {device} shell input tap {x} {y}"
            result_data["clicked_element"] = {"x": x, "y": y}

        elif action == "text":
            if not input_str:
                return json.dumps({
                    "status": "error",
                    "action": action,
                    "device": device,
                    "message": "Missing required parameter for text action (input_str)",
                })
            # 处理特殊字符
            sanitized_input_str = input_str.replace(" ", "%s").replace("'", "").replace('"', '')
            adb_command = f"adb -s {device} shell input text '{sanitized_input_str}'"
            result_data["input_str"] = input_str

        elif action == "long_press":
            if x is None or y is None:
                return json.dumps({
                    "status": "error",
                    "action": action,
                    "device": device,
                    "message": "Missing required parameters for long_press action (x, y)",
                })
            adb_command = f"adb -s {device} shell input swipe {x} {y} {x} {y} {duration}"
            result_data["long_press"] = {"x": x, "y": y, "duration": duration}

        elif action == "swipe":
            if x is None or y is None or direction is None:
                return json.dumps({
                    "status": "error",
                    "action": action,
                    "device": device,
                    "message": "Missing required parameters for swipe action (x, y, direction)",
                })
            
            unit_dist = 100  # 滑动基础距离
            offset_x, offset_y = 0, 0
            
            if direction == "up":
                offset_y = -2 * unit_dist if dist == "medium" else -3 * unit_dist
            elif direction == "down":
                offset_y = 2 * unit_dist if dist == "medium" else 3 * unit_dist
            elif direction == "left":
                offset_x = -2 * unit_dist if dist == "medium" else -3 * unit_dist
            elif direction == "right":
                offset_x = 2 * unit_dist if dist == "medium" else 3 * unit_dist
            else:
                return json.dumps({
                    "status": "error",
                    "action": action,
                    "device": device,
                    "message": "Invalid direction for swipe",
                })
            
            swipe_duration = 100 if quick else 400
            adb_command = f"adb -s {device} shell input swipe {x} {y} {x + offset_x} {y + offset_y} {swipe_duration}"
            result_data["swipe"] = {
                "start": (x, y),
                "end": (x + offset_x, y + offset_y),
                "duration": swipe_duration,
                "direction": direction,
            }

        elif action == "swipe_precise":
            if not start or not end:
                return json.dumps({
                    "status": "error",
                    "action": action,
                    "device": device,
                    "message": "Missing required parameters for swipe_precise action (start, end)",
                })
            start_x, start_y = start
            end_x, end_y = end
            adb_command = f"adb -s {device} shell input swipe {start_x} {start_y} {end_x} {end_y} {duration}"
            result_data["swipe_precise"] = {
                "start": start,
                "end": end,
                "duration": duration,
            }

        else:
            return json.dumps({
                "status": "error",
                "action": action,
                "device": device,
                "message": "Invalid action",
            })

        # 执行ADB命令
        ret = execute_adb(adb_command)
        if ret is not None and "ERROR" not in ret.upper():
            result_data["status"] = "success"
        else:
            result_data["status"] = "error"
            result_data["message"] = f"ADB command execution failed: {ret}"

        return json.dumps(result_data, ensure_ascii=False)

    except Exception as e:
        return json.dumps({
            "status": "error",
            "action": action,
            "device": device,
            "message": str(e)
        }, ensure_ascii=False)


def check_device_connection(device: str) -> bool:
    """
    检查设备连接状态
    
    Args:
        device: 设备ID
        
    Returns:
        是否连接成功
    """
    devices = list_all_devices()
    return device in devices
