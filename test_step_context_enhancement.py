#!/usr/bin/env python3
"""
测试步骤上下文增强功能

验证新的步骤上下文是否能帮助模型正确判断何时完成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_step_context_generation():
    """
    测试步骤上下文生成
    """
    print("🧪 测试步骤上下文生成")
    print("="*50)
    
    try:
        from nodes.step_executor import generate_current_step_context, get_step_completion_criteria
        from data.State import DeploymentState
        
        # 测试不同的步骤
        test_cases = [
            {
                "step": "点击我tab",
                "step_num": 1,
                "total": 4,
                "actions": []
            },
            {
                "step": "点击编辑资料", 
                "step_num": 2,
                "total": 4,
                "actions": ["click(start_box='(1103,1113)')", "click(start_box='(1141,413)')"]
            },
            {
                "step": "点击性别",
                "step_num": 3, 
                "total": 4,
                "actions": []
            }
        ]
        
        for test_case in test_cases:
            print(f"\n📋 测试步骤: {test_case['step']}")
            
            # 创建测试状态
            state = DeploymentState()
            state["current_step"] = test_case["step_num"] - 1
            state["total_steps"] = test_case["total"]
            state["task_steps"] = ["点击我tab", "点击编辑资料", "点击性别", "选择性别/生日"]
            
            # 模拟历史操作
            state["history"] = []
            for i, action in enumerate(test_case["actions"]):
                state["history"].append({
                    "action": "enhanced_get_location",
                    "step": test_case["step_num"],
                    "parsed_action": action,
                    "status": "success"
                })
            
            # 生成上下文
            context = generate_current_step_context(state, test_case["step"])
            
            print("生成的上下文:")
            print("-" * 30)
            print(context)
            print("-" * 30)
            
            # 检查关键信息
            checks = [
                ("步骤编号", f"{test_case['step_num']}/{test_case['total']}" in context),
                ("目标描述", test_case["step"] in context),
                ("完成标准", "Completion Criteria" in context),
                ("重要提示", "IMPORTANT" in context and "finished" in context)
            ]
            
            if test_case["actions"]:
                checks.append(("已执行操作", "Actions taken" in context))
            
            print("检查结果:")
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_completion_criteria():
    """
    测试完成标准生成
    """
    print("\n🧪 测试完成标准生成")
    print("="*50)
    
    try:
        from nodes.step_executor import get_step_completion_criteria
        
        test_steps = [
            ("点击我tab", "navigate to personal/profile page"),
            ("点击编辑资料", "enter the profile editing page"),
            ("点击性别", "open gender selection interface"),
            ("选择想要修改的性别/生日", "select and confirm the desired"),
            ("输入用户名", "input the required information"),
            ("其他操作", "complete the action")
        ]
        
        for step, expected_keyword in test_steps:
            criteria = get_step_completion_criteria(step)
            print(f"\n步骤: {step}")
            print(f"完成标准: {criteria}")
            
            if expected_keyword.lower() in criteria.lower():
                print("✅ 标准合理")
            else:
                print("❌ 标准可能不准确")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def test_action_tracking():
    """
    测试当前步骤操作跟踪
    """
    print("\n🧪 测试当前步骤操作跟踪")
    print("="*50)
    
    try:
        from nodes.step_executor import get_current_step_actions
        from data.State import DeploymentState
        
        # 创建测试状态
        state = DeploymentState()
        state["history"] = [
            {
                "action": "enhanced_get_location",
                "step": 2,
                "parsed_action": "click(start_box='(1103,1113)')",
                "status": "success"
            },
            {
                "action": "enhanced_get_location", 
                "step": 2,
                "parsed_action": "click(start_box='(1141,413)')",
                "status": "success"
            },
            {
                "action": "enhanced_get_location",
                "step": 3,  # 不同步骤
                "parsed_action": "click(start_box='(500,600)')",
                "status": "success"
            },
            {
                "action": "enhanced_get_location",
                "step": 2,
                "parsed_action": "wait()",
                "status": "error"  # 失败的操作
            }
        ]
        
        # 获取步骤2的操作
        actions = get_current_step_actions(state, 2)
        
        print("步骤2的成功操作:")
        for i, action in enumerate(actions, 1):
            print(f"  {i}. {action}")
        
        # 验证结果
        expected_actions = ["click(start_box='(1103,1113)')", "click(start_box='(1141,413)')"]
        
        if actions == expected_actions:
            print("✅ 操作跟踪正确")
            return True
        else:
            print(f"❌ 操作跟踪错误: 期望{expected_actions}, 实际{actions}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def test_message_enhancement():
    """
    测试消息增强效果
    """
    print("\n🧪 测试消息增强效果")
    print("="*50)
    
    try:
        from nodes.step_executor import build_messages_with_history
        from data.State import DeploymentState
        
        # 创建测试状态 - 模拟"点击编辑资料"步骤的情况
        state = DeploymentState()
        state["current_step"] = 1  # 第2步 (0-based)
        state["total_steps"] = 4
        state["task_steps"] = ["点击我tab", "点击编辑资料", "点击性别", "选择性别/生日"]
        
        # 模拟已执行的操作
        state["history"] = [
            {
                "action": "enhanced_get_location",
                "step": 2,
                "parsed_action": "click(start_box='(1103,1113)')",
                "model_response": "Thought: 我需要点击编辑按钮\nAction: click(start_box='(1103,1113)')",
                "status": "success"
            },
            {
                "action": "enhanced_get_location",
                "step": 2, 
                "parsed_action": "click(start_box='(1141,413)')",
                "model_response": "Thought: 我需要点击头像编辑\nAction: click(start_box='(1141,413)')",
                "status": "success"
            }
        ]
        
        # 构建消息
        messages = build_messages_with_history(state, "点击编辑资料")
        
        print(f"构建的消息数量: {len(messages)}")
        
        # 检查系统消息
        if messages and messages[0]["role"] == "user":
            system_content = messages[0]["content"]
            
            print("\n系统消息内容检查:")
            checks = [
                ("当前步骤", "Current Step: 2/4" in system_content),
                ("目标描述", "点击编辑资料" in system_content),
                ("完成标准", "Completion Criteria" in system_content),
                ("已执行操作", "Actions taken in this step" in system_content),
                ("重要提示", "IMPORTANT" in system_content and "finished" in system_content)
            ]
            
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}")
        
        # 检查历史消息
        assistant_messages = [m for m in messages if m["role"] == "assistant"]
        print(f"\n✅ 包含 {len(assistant_messages)} 条历史响应")
        
        print("\n📋 消息结构:")
        for i, msg in enumerate(messages):
            role = msg.get("role", "unknown")
            content_type = "text" if isinstance(msg.get("content"), str) else "image"
            print(f"  {i+1}. {role} - {content_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """
    主函数
    """
    print("🛠️ 步骤上下文增强测试工具")
    print("="*60)
    
    print("解决的问题:")
    print("❌ 模型不知道当前步骤的完成标准")
    print("❌ 模型不知道已经执行了哪些操作")
    print("❌ 模型陷入循环，不知道何时调用finished()")
    print()
    
    print("新的解决方案:")
    print("✅ 明确告诉模型当前步骤的目标")
    print("✅ 显示当前步骤内已执行的操作")
    print("✅ 提供明确的完成标准")
    print("✅ 强调何时应该调用finished()")
    print()
    
    # 运行测试
    tests = [
        ("步骤上下文生成", test_step_context_generation),
        ("完成标准生成", test_completion_criteria),
        ("操作跟踪功能", test_action_tracking),
        ("消息增强效果", test_message_enhancement),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*60)
    print("🎯 步骤上下文增强测试总结")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print(f"\n🎉 步骤上下文增强功能实现成功！")
        print(f"\n✅ 现在模型会收到:")
        print(f"  - 🎯 明确的步骤目标和完成标准")
        print(f"  - 📝 当前步骤内已执行的操作列表")
        print(f"  - ⚠️ 明确的finished()调用提示")
        print(f"  - 📚 完整的历史操作上下文")
        print(f"\n🚀 解决循环问题:")
        print(f"  步骤2: 点击编辑资料")
        print(f"    Goal: Successfully enter the profile editing page")
        print(f"    Actions taken: 1. click(edit_button) 2. click(avatar)")
        print(f"    → 模型看到已进入编辑页面 → finished() ✅")
    else:
        print(f"\n⚠️ 部分功能需要进一步完善")
        print(f"请检查失败的测试项目")
    
    print(f"\n💡 关键改进:")
    print(f"  - 模型明确知道当前步骤的目标")
    print(f"  - 模型能看到已执行的操作路径")
    print(f"  - 模型知道何时应该调用finished()")
    print(f"  - 避免无意义的循环操作")
    
    print(f"\n感谢使用步骤上下文增强测试工具！")


if __name__ == "__main__":
    main()
