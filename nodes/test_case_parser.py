#!/usr/bin/env python3
"""
测试用例解析节点

负责解析测试用例描述，提取步骤信息
"""

import re
from typing import List
from data.State import DeploymentState


def parse_test_case_node(state: DeploymentState) -> DeploymentState:
    """
    Parse test case description into individual steps
    """
    print("📋 Parsing test case into steps...")
    
    # Get test case description
    test_description = state.get("test_case_description", "")
    if not test_description:
        test_description = state.get("task", "")
    
    print(f"🔍 Parsing test case steps from: {test_description}")
    
    # Parse steps from description
    steps = parse_test_case_steps(test_description)
    
    if not steps:
        print("❌ No steps found in test case description")
        state["completed"] = True
        state["execution_status"] = "failed"
        return state
    
    print(f"✓ Parsed {len(steps)} steps: {steps}")
    
    # Store parsed information
    state["task_steps"] = steps
    state["total_steps"] = len(steps)
    state["current_step"] = 0
    state["completed_steps"] = 0
    state["step_failed"] = False
    state["retry_count"] = 0
    state["max_retries"] = state.get("max_retries", 2)
    
    print(f"✓ Parsed {len(steps)} steps for execution")
    
    return state


def parse_test_case_steps(description: str) -> List[str]:
    """
    Extract individual steps from test case description
    
    Args:
        description: Test case description text
        
    Returns:
        List of step descriptions
    """
    steps = []
    
    # Remove extra whitespace and normalize
    description = re.sub(r'\s+', ' ', description.strip())
    
    # Pattern 1: Numbered steps (1. 2. 3. etc.)
    numbered_pattern = r'(\d+)\.\s*([^0-9]+?)(?=\d+\.|$)'
    numbered_matches = re.findall(numbered_pattern, description, re.DOTALL)
    
    if numbered_matches:
        for _, step_text in numbered_matches:
            step = step_text.strip().rstrip('。').rstrip('.')
            if step and len(step) > 2:
                steps.append(step)
    
    # Pattern 2: Chinese numbered steps (一、二、三 etc.)
    if not steps:
        chinese_pattern = r'[一二三四五六七八九十]\s*[、.]\s*([^一二三四五六七八九十]+?)(?=[一二三四五六七八九十]\s*[、.]|$)'
        chinese_matches = re.findall(chinese_pattern, description, re.DOTALL)
        
        if chinese_matches:
            for step_text in chinese_matches:
                step = step_text.strip().rstrip('。').rstrip('.')
                if step and len(step) > 2:
                    steps.append(step)
    
    # Pattern 3: Bullet points or dashes
    if not steps:
        bullet_pattern = r'[-•]\s*([^-•]+?)(?=[-•]|$)'
        bullet_matches = re.findall(bullet_pattern, description, re.DOTALL)
        
        if bullet_matches:
            for step_text in bullet_matches:
                step = step_text.strip().rstrip('。').rstrip('.')
                if step and len(step) > 2:
                    steps.append(step)
    
    # Pattern 4: Line breaks as separators
    if not steps:
        lines = [line.strip() for line in description.split('\n') if line.strip()]
        for line in lines:
            # Skip headers and metadata
            if any(keyword in line for keyword in ['用例', '步骤', '期望', '结果', '描述', '名称']):
                continue
            
            # Clean up the line
            line = re.sub(r'^\d+\.\s*', '', line)  # Remove leading numbers
            line = re.sub(r'^[一二三四五六七八九十]\s*[、.]\s*', '', line)  # Remove Chinese numbers
            line = line.strip().rstrip('。').rstrip('.')
            
            if line and len(line) > 2:
                steps.append(line)
    
    # Pattern 5: Comma or semicolon separated
    if not steps:
        separators = [',', '，', ';', '；']
        for sep in separators:
            if sep in description:
                parts = description.split(sep)
                for part in parts:
                    step = part.strip().rstrip('。').rstrip('.')
                    if step and len(step) > 2:
                        steps.append(step)
                break
    
    # Pattern 6: Single step (fallback)
    if not steps and description:
        # Clean description and use as single step
        clean_desc = re.sub(r'(用例|步骤|期望|结果|描述|名称)[：:]\s*', '', description)
        clean_desc = clean_desc.strip().rstrip('。').rstrip('.')
        if clean_desc and len(clean_desc) > 2:
            steps.append(clean_desc)
    
    # Clean and validate steps
    cleaned_steps = []
    for step in steps:
        # Remove common prefixes
        step = re.sub(r'^(步骤\s*\d*[：:]?\s*)', '', step)
        step = re.sub(r'^(第\s*\d*\s*步[：:]?\s*)', '', step)
        step = step.strip()
        
        # Skip empty or too short steps
        if step and len(step) > 2:
            cleaned_steps.append(step)
    
    return cleaned_steps


def get_test_case_context(state: DeploymentState) -> str:
    """
    Generate comprehensive test case context for the model

    Args:
        state: Current execution state

    Returns:
        Formatted context string
    """
    context = []

    # Test case basic info
    test_name = state.get("test_case_name", "未命名测试")
    expected_result = state.get("expected_result", "")

    context.append(f"## 测试用例信息")
    context.append(f"用例名称: {test_name}")
    if expected_result:
        context.append(f"期望结果: {expected_result}")

    # Steps overview - 修复步骤计数逻辑
    total_steps = state.get("total_steps", 0)
    current_step = state.get("current_step", 0)

    # 当前正在执行的步骤应该是 current_step + 1
    executing_step = current_step + 1

    context.append(f"\n## 测试步骤总览 ({executing_step}/{total_steps})")

    task_steps = state.get("task_steps", [])
    for i, step in enumerate(task_steps, 1):
        if i <= current_step:
            status = "✅ 已完成"
        elif i == executing_step:
            status = "🎯 当前执行"
        else:
            status = "⏳ 待执行"

        context.append(f"{i}. {status} {step}")

    # Current step details - 显示正在执行的步骤
    if executing_step > 0 and executing_step <= len(task_steps):
        current_step_desc = task_steps[executing_step - 1]
        context.append(f"\n## 当前步骤详情")
        context.append(f"步骤编号: 第 {executing_step} 步")
        context.append(f"步骤内容: {current_step_desc}")

        # Execution history for current step
        retry_count = state.get("retry_count", 0)
        if retry_count > 0:
            context.append(f"重试次数: {retry_count}")

    # Execution history summary
    completed_steps = state.get("completed_steps", 0)
    if completed_steps > 0:
        context.append(f"\n## 执行历史")
        context.append(f"已成功完成 {completed_steps} 个步骤")

        # Show recent successful steps
        for i in range(max(0, completed_steps - 3), completed_steps):
            if i < len(task_steps):
                context.append(f"✅ 步骤 {i + 1}: {task_steps[i]}")

    return "\n".join(context)


def get_next_steps_preview(state: DeploymentState, preview_count: int = 2) -> str:
    """
    Get preview of upcoming steps

    Args:
        state: Current execution state
        preview_count: Number of upcoming steps to preview

    Returns:
        Formatted preview string
    """
    current_step = state.get("current_step", 0)
    task_steps = state.get("task_steps", [])

    # 当前正在执行的步骤
    executing_step = current_step + 1

    if executing_step > len(task_steps):
        return "所有步骤已完成"

    preview = ["## 后续步骤预览"]

    # 从当前执行的步骤开始预览
    end_index = min(executing_step + preview_count - 1, len(task_steps))
    for i in range(executing_step - 1, end_index):
        step_num = i + 1
        step_desc = task_steps[i]
        if i == executing_step - 1:
            preview.append(f"🎯 步骤 {step_num}: {step_desc} (即将执行)")
        else:
            preview.append(f"⏳ 步骤 {step_num}: {step_desc}")

    if end_index < len(task_steps):
        remaining = len(task_steps) - end_index
        preview.append(f"... 还有 {remaining} 个步骤")

    return "\n".join(preview)
