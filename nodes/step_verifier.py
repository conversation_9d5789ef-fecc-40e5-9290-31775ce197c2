#!/usr/bin/env python3
"""
步骤验证节点

负责验证步骤执行结果
"""

import base64
from datetime import datetime
from typing import Dict, Any

import config
from data.State import DeploymentState
from tools.screenshot_utils import take_screenshot
from langchain.schema.messages import HumanMessage, SystemMessage


def verify_step_execution_node(state: DeploymentState) -> DeploymentState:
    """
    Verify if the current step was executed correctly
    """
    if state["current_step"] >= len(state["task_steps"]):
        state["step_failed"] = False
        return state
        
    # Get the step that was just executed
    # current_step表示下一个要执行的步骤，所以刚执行的步骤是current_step
    executed_step_index = state["current_step"]

    if executed_step_index <= 0 or executed_step_index > len(state["task_steps"]):
        state["step_failed"] = False
        return state

    executed_step_desc = state["task_steps"][executed_step_index - 1]
    print(f"🔍 Verifying step execution: Step {executed_step_index} - {executed_step_desc}")
    
    # Get current screenshot for verification
    after_screenshot = take_screenshot(
        device=state["device"],
        app_name="deployment",
        step=executed_step_index
    )
    
    if not after_screenshot:
        print("❌ Cannot get current screenshot for verification")
        state["step_failed"] = True
        state["retry_count"] += 1
        return state
    
    try:
        # Enhanced verification with full context
        verification_result = verify_step_with_enhanced_context(
            state, executed_step_desc, after_screenshot, executed_step_index
        )

        # Parse verification result
        if "success" in verification_result.lower() or "成功" in verification_result:
            print(f"✅ Step {executed_step_index} executed successfully")
            state["step_failed"] = False
            state["retry_count"] = 0
            state["completed_steps"] = executed_step_index
            # 验证成功，current_step保持不变（已经指向下一步）
        else:
            print(f"⚠️ Step {executed_step_index} verification: {verification_result}")
            # Use intelligent failure handling
            should_retry = should_retry_step_intelligent(state, verification_result)
            state["step_failed"] = should_retry
            if should_retry:
                state["retry_count"] += 1
                # 重试时，需要回退current_step
                state["current_step"] = executed_step_index - 1
            else:
                # 如果不重试，current_step保持不变（跳过失败步骤）
                state["completed_steps"] = executed_step_index
        
        # Add verification to history
        state["history"].append({
            "step": executed_step_index,
            "step_description": executed_step_desc,
            "action": "step_verification",
            "verification_result": verification_result,
            "after_screenshot": after_screenshot,
            "status": "success" if not state["step_failed"] else "failed",
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"❌ Error during step verification: {str(e)}")
        state["step_failed"] = True
        state["retry_count"] += 1
        
        # Add error record
        state["history"].append({
            "step": executed_step_index,
            "step_description": executed_step_desc,
            "action": "step_verification",
            "error": str(e),
            "status": "error",
            "timestamp": datetime.now().isoformat()
        })
        
    return state


def verify_step_with_enhanced_context(
    state: DeploymentState,
    step_desc: str,
    screenshot_path: str,
    step_number: int
) -> str:
    """
    使用UI-TARS模型进行步骤验证
    """
    try:
        from openai import OpenAI

        # Initialize model (same as step executor)
        client = OpenAI(
            base_url="http://************:8005/v1",
            api_key="aaa"
        )

        # Read screenshot
        with open(screenshot_path, "rb") as f:
            image_content = f.read()
        image_data = base64.b64encode(image_content).decode("utf-8")

        # Create verification prompt
        verification_prompt = f"""## 角色
你是一个UI自动化测试验证专家，请根据截图判断指定的操作步骤是否已经成功执行。

## 当前验证任务
步骤内容: {step_desc}
步骤编号: 第 {step_number} 步

## 验证要求
请仔细观察截图，判断操作"{step_desc}"是否已经成功执行。

判断标准：
1. 如果是点击操作，检查是否有界面变化、按钮反馈或页面跳转
2. 如果是输入操作，检查是否有输入框激活或内容显示
3. 如果是选择操作，检查是否有选择状态变化
4. 如果是导航操作，检查是否跳转到了正确的页面

## 输出格式
请严格按照以下格式回答：
- 如果步骤已成功执行：回答 "success: [简要说明成功的证据]"
- 如果步骤执行失败：回答 "failed: [简要说明失败的原因]"
- 如果无法确定：回答 "uncertain: [说明不确定的原因]"

请基于截图给出准确判断。"""

        # Construct messages
        messages = [
            {
                "role": "user",
                "content": verification_prompt
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_data}"
                        }
                    }
                ]
            }
        ]

        # Call model for verification
        chat_completion = client.chat.completions.create(
            model="ByteDance-Seed/UI-TARS-1.5-7B",
            messages=messages,
            top_p=None,
            temperature=0.1,
            max_tokens=200,
            stream=True,
            seed=None,
            stop=None,
            frequency_penalty=None,
            presence_penalty=None
        )

        response = ""
        for message in chat_completion:
            if message.choices[0].delta.content:
                response += message.choices[0].delta.content

        verification_result = response.strip()
        print(f"🔍 Verification result: {verification_result}")

        return verification_result

    except Exception as e:
        print(f"⚠️ Verification error: {str(e)}")
        return f"uncertain: 验证过程出错 - {str(e)}"
    



def generate_verification_context(state: DeploymentState, step_desc: str, step_number: int) -> str:
    """
    Generate comprehensive context for step verification
    """
    from nodes.test_case_parser import get_test_case_context
    
    context_parts = []
    
    # Basic test case information
    context_parts.append(get_test_case_context(state))
    
    # Current step analysis
    context_parts.append(f"\n## 当前步骤分析")
    context_parts.append(f"步骤内容: {step_desc}")
    context_parts.append(f"步骤位置: 第 {step_number} 步")
    
    # Step type analysis
    step_type = analyze_step_type(step_desc)
    context_parts.append(f"操作类型: {step_type}")
    
    # Expected changes based on step type
    expected_changes = get_expected_changes(step_desc, step_type)
    if expected_changes:
        context_parts.append(f"预期变化: {expected_changes}")
    
    # Previous step context
    if step_number > 1:
        task_steps = state.get("task_steps", [])
        if step_number - 2 < len(task_steps):
            prev_step = task_steps[step_number - 2]
            context_parts.append(f"\n## 前一步骤")
            context_parts.append(f"上一步: {prev_step}")
            context_parts.append("请考虑前一步的执行结果对当前步骤验证的影响")
    
    # Next step context
    task_steps = state.get("task_steps", [])
    if step_number < len(task_steps):
        next_step = task_steps[step_number]
        context_parts.append(f"\n## 下一步骤")
        context_parts.append(f"下一步: {next_step}")
        context_parts.append("请确保当前步骤的执行为下一步做好准备")
    
    # Retry context
    retry_count = state.get("retry_count", 0)
    if retry_count > 0:
        context_parts.append(f"\n## 重试信息")
        context_parts.append(f"当前重试次数: {retry_count}")
        context_parts.append("请特别仔细验证，避免误判")
    
    return "\n".join(context_parts)


def analyze_step_type(step_desc: str) -> str:
    """
    Analyze the type of operation in the step
    """
    step_lower = step_desc.lower()
    
    if any(keyword in step_lower for keyword in ["点击", "tap", "click"]):
        if any(keyword in step_lower for keyword in ["tab", "标签", "导航"]):
            return "导航点击"
        elif any(keyword in step_lower for keyword in ["按钮", "button"]):
            return "按钮点击"
        elif any(keyword in step_lower for keyword in ["编辑", "修改"]):
            return "编辑操作"
        else:
            return "普通点击"
    
    elif any(keyword in step_lower for keyword in ["输入", "填写", "type"]):
        return "文本输入"
    
    elif any(keyword in step_lower for keyword in ["选择", "select", "勾选"]):
        return "选择操作"
    
    elif any(keyword in step_lower for keyword in ["滑动", "swipe", "scroll"]):
        return "滑动操作"
    
    elif any(keyword in step_lower for keyword in ["等待", "wait"]):
        return "等待操作"
    
    elif any(keyword in step_lower for keyword in ["返回", "back"]):
        return "返回操作"
    
    elif any(keyword in step_lower for keyword in ["查看", "验证", "确认"]):
        return "验证操作"
    
    else:
        return "其他操作"


def get_expected_changes(step_desc: str, step_type: str) -> str:
    """
    Get expected changes based on step description and type
    """
    changes = []
    
    if step_type == "导航点击":
        changes.append("底部导航栏选中状态变化")
        changes.append("页面内容切换")
        changes.append("标题栏可能变化")
    
    elif step_type == "按钮点击":
        changes.append("按钮可能有视觉反馈")
        changes.append("可能触发页面跳转")
        changes.append("可能出现新的界面元素")
    
    elif step_type == "编辑操作":
        changes.append("进入编辑模式")
        changes.append("可能出现编辑相关的UI元素")
        changes.append("界面布局可能调整")
    
    elif step_type == "文本输入":
        changes.append("输入框获得焦点")
        changes.append("可能出现软键盘")
        changes.append("输入框内容变化")
    
    elif step_type == "选择操作":
        changes.append("选择项状态变化")
        changes.append("可能出现选择确认界面")
        changes.append("相关显示内容更新")
    
    elif step_type == "滑动操作":
        changes.append("页面内容位置变化")
        changes.append("可能加载新内容")
        changes.append("滚动指示器变化")
    
    elif step_type == "等待操作":
        changes.append("页面加载完成")
        changes.append("动画效果结束")
        changes.append("界面元素稳定")
    
    elif step_type == "返回操作":
        changes.append("返回到上一级页面")
        changes.append("界面内容恢复")
        changes.append("导航状态可能变化")
    
    elif step_type == "验证操作":
        changes.append("确认相关信息显示")
        changes.append("验证界面状态")
        changes.append("检查数据更新")
    
    return "、".join(changes) if changes else "根据具体操作确定"


def should_retry_step_intelligent(state: DeploymentState, verification_result: str) -> bool:
    """
    Intelligent decision on whether to retry a step
    """
    # If verification is uncertain, be more lenient
    if "uncertain" in verification_result.lower():
        return False  # Don't retry on uncertain results
    
    # If it's a clear failure with specific reasons, consider retry
    if "failed" in verification_result.lower():
        # Check retry count
        retry_count = state.get("retry_count", 0)
        max_retries = state.get("max_retries", 2)
        
        if retry_count >= max_retries:
            return False  # Don't retry if max attempts reached
        
        # Analyze failure reason
        failure_keywords = ["无法识别", "找不到", "没有变化", "界面错误"]
        if any(keyword in verification_result for keyword in failure_keywords):
            return True  # Retry for these types of failures
    
    return False  # Default: don't retry
