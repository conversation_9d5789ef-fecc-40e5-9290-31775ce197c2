#!/usr/bin/env python3
"""
重试控制节点

负责智能重试逻辑和失败处理
"""

from typing import Dict, Any
from data.State import DeploymentState


def should_retry_step(state: DeploymentState) -> str:
    """
    智能决定是否重试步骤
    """
    if not state["step_failed"]:
        # 步骤成功，继续下一步
        return "continue"
    
    # 步骤失败，检查是否应该重试
    current_step = state.get("current_step", 0)
    retry_count = state.get("retry_count", 0)
    max_retries = state.get("max_retries", 2)
    
    if retry_count >= max_retries:
        print(f"❌ Step {current_step} failed after {max_retries} attempts")
        
        # 智能失败处理策略
        strategy = determine_failure_strategy(state)
        
        if strategy == "skip":
            print(f"⏭️ 智能跳过步骤 {current_step}，继续执行下一步")
            state["step_failed"] = False
            state["retry_count"] = 0
            
            # 记录跳过信息
            state["history"].append({
                "step": current_step,
                "action": "step_skipped",
                "reason": "达到最大重试次数，智能跳过",
                "strategy": "auto_skip"
            })
            
            return "continue"
            
        elif strategy == "retry_once":
            print(f"🔄 给步骤 {current_step} 额外一次重试机会")
            if state["current_step"] > 0:
                state["current_step"] -= 1
            state["retry_count"] = 0  # 重置重试计数
            return "retry"
            
        else:  # terminate
            print("🛑 终止测试执行")
            state["completed"] = True
            state["execution_status"] = "failed"
            return "end"
    
    else:
        # 还可以重试
        if state["current_step"] > 0:
            state["current_step"] -= 1
        print(f"🔄 Retrying step {state['current_step'] + 1} (attempt {retry_count + 1}/{max_retries})")
        return "retry"


def determine_failure_strategy(state: DeploymentState) -> str:
    """
    根据失败情况确定处理策略
    
    Returns:
        "skip": 跳过当前步骤
        "retry_once": 额外重试一次
        "terminate": 终止执行
    """
    current_step = state.get("current_step", 0)
    total_steps = state.get("total_steps", 0)
    task_steps = state.get("task_steps", [])
    
    # 分析当前步骤的重要性
    if current_step > 0 and current_step <= len(task_steps):
        current_step_desc = task_steps[current_step - 1]
        step_importance = analyze_step_importance(current_step_desc, current_step, total_steps)
        
        # 分析失败原因
        recent_verification = get_recent_verification_result(state)
        failure_reason = analyze_failure_reason(recent_verification)
        
        # 决策逻辑
        if step_importance == "critical":
            if failure_reason in ["interface_error", "element_not_found"]:
                return "retry_once"  # 关键步骤且可能是临时问题，再试一次
            else:
                return "terminate"  # 关键步骤失败，终止执行
        
        elif step_importance == "important":
            if failure_reason in ["interface_error", "uncertain"]:
                return "retry_once"  # 重要步骤，给一次额外机会
            else:
                return "skip"  # 跳过继续执行
        
        else:  # normal
            return "skip"  # 普通步骤，直接跳过
    
    # 默认策略：跳过
    return "skip"


def analyze_step_importance(step_desc: str, step_number: int, total_steps: int) -> str:
    """
    分析步骤的重要性
    
    Returns:
        "critical": 关键步骤，失败会影响整个测试
        "important": 重要步骤，失败会影响部分功能
        "normal": 普通步骤，失败影响较小
    """
    step_lower = step_desc.lower()
    
    # 关键步骤识别
    critical_keywords = [
        "登录", "login", "注册", "register",
        "保存", "save", "提交", "submit",
        "确认", "confirm", "完成", "finish"
    ]
    
    if any(keyword in step_lower for keyword in critical_keywords):
        return "critical"
    
    # 第一步和最后一步通常比较重要
    if step_number == 1 or step_number == total_steps:
        return "important"
    
    # 重要步骤识别
    important_keywords = [
        "编辑", "edit", "修改", "modify",
        "选择", "select", "设置", "setting",
        "输入", "input", "填写", "fill"
    ]
    
    if any(keyword in step_lower for keyword in important_keywords):
        return "important"
    
    # 默认为普通步骤
    return "normal"


def analyze_failure_reason(verification_result: str) -> str:
    """
    分析失败原因
    
    Returns:
        "interface_error": 界面问题
        "element_not_found": 元素未找到
        "operation_failed": 操作失败
        "uncertain": 不确定
        "other": 其他原因
    """
    if not verification_result:
        return "other"
    
    result_lower = verification_result.lower()
    
    if any(keyword in result_lower for keyword in ["界面错误", "页面错误", "加载失败"]):
        return "interface_error"
    
    if any(keyword in result_lower for keyword in ["找不到", "无法识别", "元素不存在"]):
        return "element_not_found"
    
    if any(keyword in result_lower for keyword in ["操作失败", "点击无效", "无响应"]):
        return "operation_failed"
    
    if any(keyword in result_lower for keyword in ["uncertain", "不确定", "无法判断"]):
        return "uncertain"
    
    return "other"


def get_recent_verification_result(state: DeploymentState) -> str:
    """
    获取最近的验证结果
    """
    history = state.get("history", [])
    
    # 查找最近的验证记录
    for record in reversed(history):
        if record.get("action") == "step_verification":
            return record.get("verification_result", "")
    
    return ""


def generate_retry_report(state: DeploymentState) -> Dict[str, Any]:
    """
    生成重试报告
    """
    history = state.get("history", [])
    
    # 统计重试信息
    retry_records = [r for r in history if "retry" in str(r).lower()]
    skip_records = [r for r in history if r.get("action") == "step_skipped"]
    verification_records = [r for r in history if r.get("action") == "step_verification"]
    
    # 分析失败步骤
    failed_steps = []
    for record in verification_records:
        if record.get("status") == "failed":
            step_num = record.get("step", "Unknown")
            step_desc = record.get("step_description", "Unknown")
            verification_result = record.get("verification_result", "Unknown")
            
            failed_steps.append({
                "step": step_num,
                "description": step_desc,
                "failure_reason": verification_result
            })
    
    # 分析跳过步骤
    skipped_steps = []
    for record in skip_records:
        step_num = record.get("step", "Unknown")
        reason = record.get("reason", "Unknown")
        
        skipped_steps.append({
            "step": step_num,
            "reason": reason
        })
    
    return {
        "total_retries": len(retry_records),
        "total_skips": len(skip_records),
        "failed_steps": failed_steps,
        "skipped_steps": skipped_steps,
        "retry_efficiency": calculate_retry_efficiency(state)
    }


def calculate_retry_efficiency(state: DeploymentState) -> float:
    """
    计算重试效率
    """
    total_steps = state.get("total_steps", 0)
    completed_steps = state.get("completed_steps", 0)
    
    if total_steps == 0:
        return 0.0
    
    return completed_steps / total_steps


def should_continue_execution(state: DeploymentState) -> bool:
    """
    判断是否应该继续执行
    """
    current_step = state.get("current_step", 0)
    total_steps = state.get("total_steps", 0)
    
    # 检查是否还有步骤要执行
    if current_step >= total_steps:
        return False
    
    # 检查是否已标记为完成
    if state.get("completed", False):
        return False
    
    # 检查连续失败情况
    consecutive_failures = count_consecutive_failures(state)
    if consecutive_failures >= 3:  # 连续3步失败，考虑停止
        print(f"⚠️ 检测到连续 {consecutive_failures} 步失败，建议停止执行")
        return False
    
    return True


def count_consecutive_failures(state: DeploymentState) -> int:
    """
    计算连续失败的步骤数
    """
    history = state.get("history", [])
    consecutive_count = 0
    
    # 从最近的记录开始检查
    for record in reversed(history):
        if record.get("action") == "step_verification":
            if record.get("status") == "failed":
                consecutive_count += 1
            else:
                break  # 遇到成功的就停止计数
    
    return consecutive_count
