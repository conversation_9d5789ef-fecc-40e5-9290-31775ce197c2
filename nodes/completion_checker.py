#!/usr/bin/env python3
"""
完成检查节点

负责检查测试用例是否完成
"""

import base64
from typing import Dict, Any
from data.State import DeploymentState
from langchain.schema.messages import HumanMessage, SystemMessage


def check_task_completion_node(state: DeploymentState) -> DeploymentState:
    """
    Check if the test case is completed
    """
    current_step = state.get("current_step", 0)
    total_steps = state.get("total_steps", 0)
    
    # Check if all steps are completed
    if current_step >= total_steps:
        print("🎉 All test case steps completed successfully!")
        
        # Perform final verification
        final_result = perform_final_verification(state)
        
        state["completed"] = True
        state["execution_status"] = "completed"
        state["final_verification"] = final_result
        
        # Generate completion report
        completion_report = generate_completion_report(state)
        state["completion_report"] = completion_report
        
        print(f"📊 Test case completion report:")
        print(f"  Total steps: {total_steps}")
        print(f"  Completed steps: {state.get('completed_steps', 0)}")
        print(f"  Success rate: {completion_report.get('success_rate', 0):.1%}")
        
        return state
    
    # Check if execution should continue
    from nodes.retry_controller import should_continue_execution
    
    if not should_continue_execution(state):
        print("🛑 Test case execution stopped due to multiple failures")
        state["completed"] = True
        state["execution_status"] = "stopped"
        
        # Generate partial completion report
        completion_report = generate_completion_report(state)
        state["completion_report"] = completion_report
        
        return state
    
    # Continue execution
    state["completed"] = False
    return state


def perform_final_verification(state: DeploymentState) -> Dict[str, Any]:
    """
    Perform final verification of the test case
    """
    print("🔍 Performing final test case verification...")
    
    try:
        # Get test case information
        test_name = state.get("test_case_name", "Unknown Test")
        expected_result = state.get("expected_result", "")
        
        # Get current screenshot
        current_screenshot = state["current_page"]["screenshot"]
        
        if not current_screenshot:
            return {
                "status": "error",
                "message": "Cannot get final screenshot for verification"
            }
        
        # Perform AI-based final verification
        verification_result = verify_final_state_with_ai(
            state, test_name, expected_result, current_screenshot
        )
        
        return {
            "status": "completed",
            "verification_result": verification_result,
            "screenshot": current_screenshot
        }
        
    except Exception as e:
        print(f"❌ Error during final verification: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }


def verify_final_state_with_ai(
    state: DeploymentState,
    test_name: str,
    expected_result: str,
    screenshot_path: str
) -> str:
    """
    Use AI to verify the final state against expected results
    """
    from langchain_openai import AzureChatOpenAI
    
    # Initialize model
    model = AzureChatOpenAI(
        azure_endpoint="https://gpt-4o-sweden-central.openai.azure.com/",
        api_version="2024-02-15-preview",
        azure_deployment="gpt-4o",
        temperature=0,
        max_tokens=4000,
    )
    
    try:
        # Read screenshot
        with open(screenshot_path, "rb") as f:
            image_content = f.read()
        image_data = base64.b64encode(image_content).decode("utf-8")
        
        # Generate final verification context
        verification_context = generate_final_verification_context(state)
        
        # Create final verification prompt
        system_prompt = f"""你是一个UI自动化测试最终验证专家。请根据测试用例的完整执行情况和最终截图，判断测试用例是否达到了预期目标。

{verification_context}

## 最终验证标准
1. **目标达成**: 测试用例的主要目标是否实现
2. **预期结果**: 是否符合预期的最终结果
3. **功能完整性**: 相关功能是否正常工作
4. **界面状态**: 最终界面状态是否正确
5. **数据一致性**: 相关数据是否正确更新

## 输出格式
请提供详细的验证结果，包括：
- 总体评估: success/partial_success/failed
- 具体分析: 详细说明各个方面的验证结果
- 发现的问题: 如果有问题，请具体说明
- 改进建议: 如果有改进空间，请提供建议

请基于截图和执行历史给出客观、准确的最终评估。"""
        
        human_prompt = f"""请对以下测试用例进行最终验证：

**测试用例**: {test_name}
**期望结果**: {expected_result}

请查看最终截图和执行历史，判断测试用例是否成功完成，并提供详细的验证分析。"""
        
        # Construct messages
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=[
                {"type": "text", "text": human_prompt},
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/png;base64,{image_data}"}
                }
            ])
        ]
        
        # Call AI for final verification
        response = model.invoke(messages)
        return response.content.strip()
        
    except Exception as e:
        return f"final_verification_error: {str(e)}"


def generate_final_verification_context(state: DeploymentState) -> str:
    """
    Generate context for final verification
    """
    from nodes.test_case_parser import get_test_case_context
    from nodes.retry_controller import generate_retry_report
    
    context_parts = []
    
    # Basic test case information
    context_parts.append(get_test_case_context(state))
    
    # Execution summary
    total_steps = state.get("total_steps", 0)
    completed_steps = state.get("completed_steps", 0)
    current_step = state.get("current_step", 0)
    
    context_parts.append(f"\n## 执行总结")
    context_parts.append(f"总步骤数: {total_steps}")
    context_parts.append(f"已完成步骤: {completed_steps}")
    context_parts.append(f"当前步骤: {current_step}")
    context_parts.append(f"完成率: {completed_steps/total_steps*100:.1f}%" if total_steps > 0 else "完成率: 0%")
    
    # Step execution details
    task_steps = state.get("task_steps", [])
    context_parts.append(f"\n## 步骤执行详情")
    
    for i, step in enumerate(task_steps, 1):
        if i <= completed_steps:
            status = "✅ 成功完成"
        elif i <= current_step:
            status = "⚠️ 已执行但可能有问题"
        else:
            status = "❌ 未执行"
        
        context_parts.append(f"{i}. {status} {step}")
    
    # Retry and failure analysis
    retry_report = generate_retry_report(state)
    if retry_report["total_retries"] > 0 or retry_report["total_skips"] > 0:
        context_parts.append(f"\n## 重试和失败分析")
        context_parts.append(f"总重试次数: {retry_report['total_retries']}")
        context_parts.append(f"跳过步骤数: {retry_report['total_skips']}")
        context_parts.append(f"执行效率: {retry_report['retry_efficiency']:.1%}")
        
        if retry_report["failed_steps"]:
            context_parts.append(f"\n失败步骤:")
            for failed_step in retry_report["failed_steps"]:
                context_parts.append(f"- 步骤{failed_step['step']}: {failed_step['description']}")
                context_parts.append(f"  失败原因: {failed_step['failure_reason']}")
        
        if retry_report["skipped_steps"]:
            context_parts.append(f"\n跳过步骤:")
            for skipped_step in retry_report["skipped_steps"]:
                context_parts.append(f"- 步骤{skipped_step['step']}: {skipped_step['reason']}")
    
    return "\n".join(context_parts)


def generate_completion_report(state: DeploymentState) -> Dict[str, Any]:
    """
    Generate comprehensive completion report
    """
    from nodes.retry_controller import generate_retry_report
    
    # Basic metrics
    total_steps = state.get("total_steps", 0)
    completed_steps = state.get("completed_steps", 0)
    current_step = state.get("current_step", 0)
    
    success_rate = completed_steps / total_steps if total_steps > 0 else 0
    
    # Execution status
    execution_status = state.get("execution_status", "unknown")
    
    # Time analysis
    history = state.get("history", [])
    start_time = None
    end_time = None
    
    for record in history:
        timestamp = record.get("timestamp")
        if timestamp:
            if start_time is None:
                start_time = timestamp
            end_time = timestamp
    
    # Step analysis
    step_analysis = []
    task_steps = state.get("task_steps", [])
    
    for i, step in enumerate(task_steps, 1):
        step_info = {
            "step_number": i,
            "description": step,
            "status": "completed" if i <= completed_steps else "failed" if i <= current_step else "not_executed"
        }
        
        # Find verification result for this step
        for record in reversed(history):
            if (record.get("action") == "step_verification" and 
                record.get("step") == i):
                step_info["verification_result"] = record.get("verification_result", "")
                break
        
        step_analysis.append(step_info)
    
    # Retry analysis
    retry_report = generate_retry_report(state)
    
    # Final verification
    final_verification = state.get("final_verification", {})
    
    return {
        "test_case_name": state.get("test_case_name", "Unknown"),
        "execution_status": execution_status,
        "total_steps": total_steps,
        "completed_steps": completed_steps,
        "current_step": current_step,
        "success_rate": success_rate,
        "start_time": start_time,
        "end_time": end_time,
        "step_analysis": step_analysis,
        "retry_analysis": retry_report,
        "final_verification": final_verification,
        "overall_success": success_rate >= 0.8 and execution_status == "completed"
    }
