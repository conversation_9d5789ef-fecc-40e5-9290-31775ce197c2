#!/usr/bin/env python3
"""
步骤执行节点

负责执行当前测试步骤
"""

import base64
from datetime import datetime
from typing import Dict, Any
from data.State import DeploymentState
from tool.screen_content import capture_and_parse_screen


def execute_current_step_node(state: DeploymentState) -> DeploymentState:
    """
    Execute the current step of the test case
    """
    if state["current_step"] >= len(state["task_steps"]):
        print("✓ All steps completed")
        state["completed"] = True
        return state
    
    current_step_desc = state["task_steps"][state["current_step"]]
    print(f"🎯 Executing step {state['current_step'] + 1}/{len(state['task_steps'])}: {current_step_desc}")
    
    # Save screenshot before execution
    before_screenshot = state["current_page"]["screenshot"]
    
    # Update task to current step for execution
    original_task = state["task"]
    state["task"] = current_step_desc
    
    try:
        # Execute current step using enhanced get_location
        state = enhanced_get_location(state)
        
        # Check if execution was successful
        execution_success = True
        if len(state["history"]) > 0:
            last_record = state["history"][-1]
            if last_record.get("status") == "error":
                execution_success = False
        
        # Add execution record
        execution_record = {
            "step": state["current_step"] + 1,
            "step_description": current_step_desc,
            "action": "step_execution",
            "before_screenshot": before_screenshot,
            "execution_success": execution_success,
            "timestamp": datetime.now().isoformat()
        }
        
        state["history"].append(execution_record)
        
        # Only increment step counter after execution (verification will check this step)
        state["current_step"] += 1
        
    except Exception as e:
        print(f"❌ Error executing step {state['current_step'] + 1}: {str(e)}")
        # Add error record
        error_record = {
            "step": state["current_step"] + 1,
            "step_description": current_step_desc,
            "action": "step_execution",
            "error": str(e),
            "status": "error"
        }
        state["history"].append(error_record)
        
        # Don't increment step counter on error
        state["step_failed"] = True
        state["retry_count"] += 1
    
    finally:
        # Restore original task
        state["task"] = original_task
    
    return state


def enhanced_get_location(state: DeploymentState) -> DeploymentState:
    """
    Enhanced version of get_location with comprehensive context
    """
    from tool.utils import parse_action_with_coordinates
    from langchain_openai import AzureChatOpenAI
    
    # Initialize model
    model = AzureChatOpenAI(
        azure_endpoint="https://gpt-4o-sweden-central.openai.azure.com/",
        api_version="2024-02-15-preview",
        azure_deployment="gpt-4o",
        temperature=0,
        max_tokens=4000,
    )
    
    # Get current screenshot
    with open(state["current_page"]["screenshot"], "rb") as f:
        image_content = f.read()
    image_data_base64 = base64.b64encode(image_content).decode("utf-8")
    
    # Generate comprehensive context
    test_context = generate_execution_context(state)
    current_task = state.get("task", "")
    
    # Create enhanced messages with full context
    messages = [
        {
            "role": "user",
            "content": f'''
            ## 角色
            你是一个安卓智能手机操作助手，请你严格依据截图和测试用例上下文判断下一步操作。
            
            ## 测试用例上下文
            {test_context}
            
            ## 当前任务
            {current_task}
            
            ## 输入说明
            你将根据用户提供的手机截图，判断当前界面状态，并根据任务目标，输出下一步合理的 adb 操作指令。
            请特别注意当前执行的步骤在整个测试用例中的位置和作用。

            ## 输出格式（务必严格遵循）
            Thought: [分析当前界面状态，结合测试用例上下文，说明为什么选择这个操作]
            Action: [具体的操作指令]
            
            ## Action Space（只能使用以下动作）
            - click(start_box='<|box_start|>(x1, y1)<|box_end|>') # 点击屏幕
            - type(content='input text') # 输入文本
            - long_press(x=123, y=456) # 长按某个屏幕
            - drag(start_box='<|box_start|>(x1, y1)<|box_end|>', end_box='<|box_start|>(x3, y3)<|box_end|>') # 滑动
            - wait() # 等待
            - finished(content='xxx') # 当前步骤已完成，输出总结
            
            ## 注意事项
            - 只能使用 Action Space 中的操作，不要输出"点击按钮"、"执行跳转"等非 adb 指令
            - 点击、按压禁止使用click，必须使用tap
            - Action 必须只有一条指令，不能混合
            - 坐标必须是整数格式，内容必须合理
            - 如果当前步骤已完成或无法继续，可以输出 finished(content='步骤完成说明')
            - 请结合测试用例的整体目标和当前步骤的具体要求来判断操作
            '''
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_data_base64}"
                    }
                }
            ]
        }
    ]
    
    # Call model
    response = model.invoke(messages)
    model_response = response.content.strip()
    
    print(f"Model response: {model_response}")
    
    # Parse action
    parsed_action = parse_action_with_coordinates(model_response)
    
    if parsed_action:
        # Execute action using existing action agent
        from tool.action_agent import action_agent
        action_result = action_agent(parsed_action, state["device"])
        
        print(f"✓ Enhanced execution successful: {parsed_action}")
        
        # Add to history
        state["history"].append({
            "action": "enhanced_get_location",
            "task": current_task,
            "model_response": model_response,
            "parsed_action": parsed_action,
            "action_result": action_result,
            "status": "success" if action_result.get("status") == "success" else "error"
        })
    else:
        print(f"❌ Failed to parse action from: {model_response}")
        state["history"].append({
            "action": "enhanced_get_location",
            "task": current_task,
            "model_response": model_response,
            "error": "Failed to parse action",
            "status": "error"
        })
    
    return state


def generate_execution_context(state: DeploymentState) -> str:
    """
    Generate comprehensive execution context for the model
    """
    from nodes.test_case_parser import get_test_case_context, get_next_steps_preview
    
    context_parts = []
    
    # Basic test case context
    context_parts.append(get_test_case_context(state))
    
    # Current execution status
    current_step = state.get("current_step", 0)
    total_steps = state.get("total_steps", 0)
    retry_count = state.get("retry_count", 0)
    
    context_parts.append(f"\n## 执行状态")
    context_parts.append(f"当前进度: {current_step}/{total_steps}")
    
    if retry_count > 0:
        context_parts.append(f"当前步骤重试次数: {retry_count}")
        context_parts.append("⚠️ 请仔细分析界面状态，确保操作准确")
    
    # Recent execution history
    history = state.get("history", [])
    recent_actions = [h for h in history[-3:] if h.get("action") in ["enhanced_get_location", "step_execution"]]
    
    if recent_actions:
        context_parts.append(f"\n## 最近操作历史")
        for i, action in enumerate(recent_actions, 1):
            step_num = action.get("step", "Unknown")
            action_type = action.get("action", "Unknown")
            status = action.get("status", "Unknown")
            
            if "parsed_action" in action:
                parsed_action = action["parsed_action"]
                context_parts.append(f"{i}. 步骤{step_num} - {action_type}: {parsed_action} ({status})")
            else:
                context_parts.append(f"{i}. 步骤{step_num} - {action_type}: {status}")
    
    # Next steps preview
    context_parts.append(f"\n{get_next_steps_preview(state)}")
    
    # Execution tips based on current step
    if current_step > 0 and current_step <= len(state.get("task_steps", [])):
        current_step_desc = state["task_steps"][current_step - 1]
        tips = get_execution_tips(current_step_desc)
        if tips:
            context_parts.append(f"\n## 执行提示")
            context_parts.append(tips)
    
    return "\n".join(context_parts)


def get_execution_tips(step_description: str) -> str:
    """
    Get execution tips based on step description
    """
    step_lower = step_description.lower()
    tips = []
    
    if any(keyword in step_lower for keyword in ["点击", "tap", "click"]):
        tips.append("- 仔细识别目标元素的位置")
        tips.append("- 确保点击坐标准确")
        tips.append("- 注意区分相似的界面元素")
    
    if any(keyword in step_lower for keyword in ["输入", "填写", "type"]):
        tips.append("- 先确保输入框已获得焦点")
        tips.append("- 检查是否需要清空现有内容")
        tips.append("- 注意输入法状态")
    
    if any(keyword in step_lower for keyword in ["滑动", "swipe", "scroll"]):
        tips.append("- 确定滑动的起始和结束位置")
        tips.append("- 注意滑动方向和距离")
        tips.append("- 检查是否有滑动限制")
    
    if any(keyword in step_lower for keyword in ["等待", "wait"]):
        tips.append("- 观察页面加载状态")
        tips.append("- 等待动画或过渡效果完成")
        tips.append("- 确认页面元素已稳定")
    
    if any(keyword in step_lower for keyword in ["选择", "select"]):
        tips.append("- 识别选择项的当前状态")
        tips.append("- 确认选择操作的反馈")
        tips.append("- 注意多选和单选的区别")
    
    return "\n".join(tips) if tips else ""
