#!/usr/bin/env python3
"""
步骤执行节点

负责执行当前测试步骤
"""

import base64
from datetime import datetime

from openai import OpenAI

from tools.screenshot_utils import take_screenshot

from data.State import DeploymentState


def execute_current_step_node(state: DeploymentState) -> DeploymentState:
    """
    Execute the current step of the test case
    """
    if state["current_step"] >= len(state["task_steps"]):
        print("✓ All steps completed")
        state["completed"] = True
        return state
    
    current_step_desc = state["task_steps"][state["current_step"]]
    print(f"🎯 Executing step {state['current_step'] + 1}/{len(state['task_steps'])}: {current_step_desc}")
    
    # Save screenshot before execution
    before_screenshot = state["current_page"].get("screenshot", "")
    
    # Update task to current step for execution
    original_task = state["task"]
    state["task"] = current_step_desc
    
    try:
        # Execute current step using enhanced get_location
        state = enhanced_get_location(state)
        
        # Check if execution was successful
        execution_success = True
        if len(state["history"]) > 0:
            last_record = state["history"][-1]
            if last_record.get("status") == "error":
                execution_success = False
        
        # Add execution record (不自动递增步骤计数器)
        execution_record = {
            "step": state["current_step"] + 1,
            "step_description": current_step_desc,
            "action": "step_execution_start",
            "before_screenshot": before_screenshot,
            "execution_success": execution_success,
            "timestamp": datetime.now().isoformat()
        }

        state["history"].append(execution_record)

        # 不自动递增步骤计数器，只有收到finished()才递增
        
    except Exception as e:
        print(f"❌ Error executing step {state['current_step'] + 1}: {str(e)}")
        # Add error record
        error_record = {
            "step": state["current_step"] + 1,
            "step_description": current_step_desc,
            "action": "step_execution",
            "error": str(e),
            "status": "error"
        }
        state["history"].append(error_record)

        # 错误时也不递增步骤计数器，继续当前步骤
        state["step_failed"] = True
        state["retry_count"] += 1
    
    finally:
        # Restore original task
        state["task"] = original_task
    
    return state


def build_messages_with_history(state: DeploymentState, current_task: str) -> list:
    """
    构建包含执行历史的消息列表
    """
    messages = []

    # 添加系统指令
    system_instruction = f'''
You are a GUI agent. 
You are given a task and your action history, with screenshots. 
You need to perform the next action to complete the task.

## Output Format
```
Thought: ...
Action: ...
```

## Action Space

click(start_box='<|box_start|>(x1, y1)<|box_end|>')
left_double(start_box='<|box_start|>(x1, y1)<|box_end|>')
right_single(start_box='<|box_start|>(x1, y1)<|box_end|>')
drag(start_box='<|box_start|>(x1, y1)<|box_end|>', end_box='<|box_start|>(x3, y3)<|box_end|>')
type(content='') #If you want to submit your input, use "\\n" at the end of `content`.
scroll(start_box='<|box_start|>(x1, y1)<|box_end|>', direction='down or up or right or left')
wait() #Sleep for 5s and take a screenshot to check for any changes.
finished(content='xxx') # Use escape characters \\', \\", and \\n in content part to ensure we can parse the content in normal python string format.

## Note
- Use Chinese in `Thought` part.
- Write a small plan and finally summarize your next action (with its target element) in one sentence in `Thought` part.

## User Instruction
{current_task}'''

    messages.append({
        "role": "user",
        "content": system_instruction
    })

    # 添加执行历史 - 不限制数量，包含所有成功的执行记录
    history = state.get("history", [])
    execution_records = [r for r in history if r.get("action") == "enhanced_get_location" and r.get("status") == "success"]

    # 添加所有历史记录，不做数量限制
    for record in execution_records:
        # 添加助手的响应（之前的操作）
        if record.get("model_response"):
            messages.append({
                "role": "assistant",
                "content": record["model_response"]
            })

    return messages


def enhanced_get_location(state: DeploymentState) -> DeploymentState:
    """
    Enhanced version of get_location with comprehensive context
    """
    from tools.action_agent import execute_simple_action
    from openai import OpenAI

    # Initialize model
    client = OpenAI(
        base_url="http://10.65.230.19:8005/v1",
        api_key="aaa"
    )

    # 每次循环都获取最新的截图
    screenshot_path = take_screenshot(
        device=state["device"],
        app_name="deployment",
        step=state.get("current_step", 0)
    )

    # 更新当前页面截图
    state["current_page"]["screenshot"] = screenshot_path

    try:
        with open(screenshot_path, "rb") as f:
            image_content = f.read()
        image_data_base64 = base64.b64encode(image_content).decode("utf-8")
    except Exception as e:
        print(f"❌ Failed to read screenshot: {str(e)}")
        state["history"].append({
            "action": "enhanced_get_location",
            "error": f"Failed to read screenshot: {str(e)}",
            "status": "error"
        })
        return state

    # Get current task
    current_task = state.get("task", "")

    # Build messages with execution history
    messages = build_messages_with_history(state, current_task)

    # Add current screenshot
    messages.append({
        "role": "user",
        "content": [
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{image_data_base64}"
                }
            }
        ]
    })

    try:
        # Call model

        chat_completion = client.chat.completions.create(
            model="ByteDance-Seed/UI-TARS-1.5-7B",
            messages=messages,
            top_p=None,
            temperature=0.1,
            max_tokens=400,
            stream=True,
            seed=None,
            stop=None,
            frequency_penalty=None,
            presence_penalty=None
        )

        response = ""
        for message in chat_completion:
            if message.choices[0].delta.content:
                response += message.choices[0].delta.content

        model_response = response
        print(f"Model response: {model_response}")
        # Extract action from response
        action_line = ""
        for line in model_response.split('\n'):
            if line.strip().startswith('Action:'):
                action_line = line.replace('Action:', '').strip()
                break

        if action_line:
            # Check if this is a finished action
            if action_line.startswith("finished("):
                print(f"✅ Step {state.get('current_step', 0) + 1} completed: {action_line}")

                # 只有finished()了才标记步骤完成并递增计数器
                state["completed_steps"] = state.get("current_step", 0) + 1
                state["current_step"] = state.get("current_step", 0) + 1  # 递增到下一步
                state["step_failed"] = False
                state["retry_count"] = 0  # 重置重试计数

                # Add completion record
                state["history"].append({
                    "action": "step_completed",
                    "task": current_task,
                    "model_response": model_response,
                    "completion_action": action_line,
                    "step": state["completed_steps"],
                    "status": "completed"
                })

                print(f"🎯 Ready for next step: {state['current_step'] + 1}/{state.get('total_steps', 0)}")
                return state

            # Execute action using simple action executor
            action_result = execute_simple_action(action_line, state["device"])

            print(f"✓ Action executed: {action_line}")
            print(f"⏳ Continuing current step until finished() is called...")

            # Add to history (不递增步骤计数器，继续当前步骤)
            state["history"].append({
                "action": "enhanced_get_location",
                "task": current_task,
                "model_response": model_response,
                "parsed_action": action_line,
                "action_result": action_result,
                "step": state.get("current_step", 0) + 1,  # 当前正在执行的步骤
                "status": "success" if action_result.get("status") == "success" else "error"
            })

            # 不递增current_step，继续当前步骤直到收到finished()
        else:
            print(f"❌ Failed to extract action from: {model_response}")
            state["history"].append({
                "action": "enhanced_get_location",
                "task": current_task,
                "model_response": model_response,
                "error": "Failed to extract action",
                "status": "error"
            })

    except Exception as e:
        print(f"❌ Error in enhanced_get_location: {str(e)}")
        state["history"].append({
            "action": "enhanced_get_location",
            "task": current_task,
            "error": str(e),
            "status": "error"
        })

    return state


def generate_execution_context(state: DeploymentState) -> str:
    """
    Generate comprehensive execution context for the model
    """
    from nodes.test_case_parser import get_test_case_context, get_next_steps_preview
    
    context_parts = []
    
    # Basic test case context
    context_parts.append(get_test_case_context(state))
    
    # Current execution status
    current_step = state.get("current_step", 0)
    total_steps = state.get("total_steps", 0)
    retry_count = state.get("retry_count", 0)
    
    context_parts.append(f"\n## 执行状态")
    context_parts.append(f"当前进度: {current_step}/{total_steps}")
    
    if retry_count > 0:
        context_parts.append(f"当前步骤重试次数: {retry_count}")
        context_parts.append("⚠️ 请仔细分析界面状态，确保操作准确")
    
    # Recent execution history
    history = state.get("history", [])
    recent_actions = [h for h in history[-3:] if h.get("action") in ["enhanced_get_location", "step_execution"]]
    
    if recent_actions:
        context_parts.append(f"\n## 最近操作历史")
        for i, action in enumerate(recent_actions, 1):
            step_num = action.get("step", "Unknown")
            action_type = action.get("action", "Unknown")
            status = action.get("status", "Unknown")
            
            if "parsed_action" in action:
                parsed_action = action["parsed_action"]
                context_parts.append(f"{i}. 步骤{step_num} - {action_type}: {parsed_action} ({status})")
            else:
                context_parts.append(f"{i}. 步骤{step_num} - {action_type}: {status}")
    
    # Next steps preview
    context_parts.append(f"\n{get_next_steps_preview(state)}")
    
    # Execution tips based on current step
    if current_step > 0 and current_step <= len(state.get("task_steps", [])):
        current_step_desc = state["task_steps"][current_step - 1]
        tips = get_execution_tips(current_step_desc)
        if tips:
            context_parts.append(f"\n## 执行提示")
            context_parts.append(tips)
    
    return "\n".join(context_parts)


def get_execution_tips(step_description: str) -> str:
    """
    Get execution tips based on step description
    """
    step_lower = step_description.lower()
    tips = []
    
    if any(keyword in step_lower for keyword in ["点击", "tap", "click"]):
        tips.append("- 仔细识别目标元素的位置")
        tips.append("- 确保点击坐标准确")
        tips.append("- 注意区分相似的界面元素")
    
    if any(keyword in step_lower for keyword in ["输入", "填写", "type"]):
        tips.append("- 先确保输入框已获得焦点")
        tips.append("- 检查是否需要清空现有内容")
        tips.append("- 注意输入法状态")
    
    if any(keyword in step_lower for keyword in ["滑动", "swipe", "scroll"]):
        tips.append("- 确定滑动的起始和结束位置")
        tips.append("- 注意滑动方向和距离")
        tips.append("- 检查是否有滑动限制")
    
    if any(keyword in step_lower for keyword in ["等待", "wait"]):
        tips.append("- 观察页面加载状态")
        tips.append("- 等待动画或过渡效果完成")
        tips.append("- 确认页面元素已稳定")
    
    if any(keyword in step_lower for keyword in ["选择", "select"]):
        tips.append("- 识别选择项的当前状态")
        tips.append("- 确认选择操作的反馈")
        tips.append("- 注意多选和单选的区别")
    
    return "\n".join(tips) if tips else ""
