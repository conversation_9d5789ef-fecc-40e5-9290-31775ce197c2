#!/usr/bin/env python3
"""
增强版部署系统

重构后的多步骤测试用例执行系统，按节点拆分，增强上下文信息
"""

from datetime import datetime
from typing import Any, Dict

from langgraph.graph import StateGraph, END

from data.State import DeploymentState
from nodes.completion_checker import check_task_completion_node
from nodes.retry_controller import should_retry_step
from nodes.step_executor import execute_current_step_node
# from nodes.step_verifier import verify_step_execution_node  # 不再需要验证节点
# Import node modules
from nodes.test_case_parser import parse_test_case_node
from tools.screenshot_utils import take_screenshot


def initialize_enhanced_deployment_state(
    test_case_name: str,
    test_case_description: str,
    expected_result: str,
    device: str = "emulator-5554"
) -> DeploymentState:
    """
    Initialize enhanced deployment state with comprehensive information
    """
    print(f"🚀 Starting enhanced multi-step test case: {test_case_name}")
    print(f"📝 Description: {test_case_description}")
    print(f"🎯 Expected result: {expected_result}")
    
    # Initialize state with enhanced information
    state = DeploymentState()

    # Set all the required fields
    state["test_case_name"] = test_case_name
    state["test_case_description"] = test_case_description
    state["expected_result"] = expected_result
    state["device"] = device

    # Task information (for compatibility)
    state["task"] = test_case_description

    # Execution state
    state["current_step"] = 0
    state["total_steps"] = 0
    state["completed_steps"] = 0
    state["step_failed"] = False
    state["retry_count"] = 0
    state["max_retries"] = 2

    # Status tracking
    state["completed"] = False
    state["execution_status"] = "running"

    # History and context
    state["history"] = []
    state["execution_context"] = {}

    # Screenshots and UI state
    state["current_page"] = {}

    # Results
    state["task_steps"] = []
    state["steps_executed"] = []
    state["execution_history"] = []
    
    # Add initialization record
    state["history"].append({
        "action": "initialization",
        "test_case_name": test_case_name,
        "expected_result": expected_result,
        "device": device,
        "timestamp": datetime.now().isoformat()
    })
    
    return state


def capture_initial_screen_node(state: DeploymentState) -> DeploymentState:
    """
    Capture initial screen state
    """
    print("📸 Capturing and parsing current screen...")
    
    try:
        # Capture initial screen
        screenshot_path = take_screenshot(
            device=state["device"],
            app_name="deployment",
            step=0
        )

        if screenshot_path and not screenshot_path.startswith("Screenshot failed"):
            state["current_page"]["screenshot"] = screenshot_path
            print("✓ Screen captured successfully")

            # Add screen capture record
            state["history"].append({
                "action": "initial_screen_capture",
                "screenshot": screenshot_path,
                "timestamp": datetime.now().isoformat()
            })
        else:
            raise Exception(f"Screenshot failed: {screenshot_path}")
        
    except Exception as e:
        print(f"❌ Failed to capture initial screen: {str(e)}")
        state["completed"] = True
        state["execution_status"] = "failed"
        
        # Add error record
        state["history"].append({
            "action": "initial_screen_capture",
            "error": str(e),
            "status": "error",
            "timestamp": datetime.now().isoformat()
        })
    
    return state


def route_next_action(state: DeploymentState) -> str:
    """
    路由逻辑：只有finished()才进入下一步，否则继续当前步骤
    """
    # Check if completed
    if state.get("completed", False):
        return "end"

    # Check if all steps are done
    current_step = state.get("current_step", 0)
    total_steps = state.get("total_steps", 0)

    if current_step >= total_steps:
        return "check_completion"

    # 检查是否有步骤失败需要重试
    if state.get("step_failed", False):
        retry_count = state.get("retry_count", 0)
        max_retries = state.get("max_retries", 3)

        if retry_count >= max_retries:
            print(f"⏭️ Step {current_step + 1} failed after {max_retries} retries, skipping...")
            # 强制跳过失败步骤
            state["current_step"] += 1
            state["step_failed"] = False
            state["retry_count"] = 0
            return "execute_step"

    # 默认继续执行当前步骤（直到收到finished()）
    return "execute_step"


def create_enhanced_deployment_graph() -> StateGraph:
    """
    Create enhanced deployment graph with modular nodes
    """
    # Create graph
    graph = StateGraph(DeploymentState)
    
    # Add nodes (去掉验证节点)
    graph.add_node("capture_screen", capture_initial_screen_node)
    graph.add_node("parse_test_case", parse_test_case_node)
    graph.add_node("execute_step", execute_current_step_node)
    graph.add_node("check_completion", check_task_completion_node)
    
    # Set entry point
    graph.set_entry_point("capture_screen")
    
    # Add edges (简化流程，去掉验证节点)
    graph.add_edge("capture_screen", "parse_test_case")
    graph.add_edge("parse_test_case", "execute_step")

    # Add conditional routing (直接从执行节点路由)
    graph.add_conditional_edges(
        "execute_step",
        route_next_action,
        {
            "execute_step": "execute_step",
            "check_completion": "check_completion",
            "end": END
        }
    )
    
    graph.add_conditional_edges(
        "check_completion",
        lambda state: "end" if state.get("completed", False) else "execute_step",
        {
            "execute_step": "execute_step",
            "end": END
        }
    )
    
    return graph


def run_enhanced_multi_step_test_case(
    test_case_name: str,
    test_case_description: str,
    expected_result: str,
    device: str = "emulator-5554"
) -> Dict[str, Any]:
    """
    Run enhanced multi-step test case with comprehensive context
    """
    try:
        # Initialize state
        initial_state = initialize_enhanced_deployment_state(
            test_case_name, test_case_description, expected_result, device
        )
        
        # Create and compile graph
        graph = create_enhanced_deployment_graph()
        compiled_graph = graph.compile()
        
        # Execute graph
        final_state = compiled_graph.invoke(initial_state)
        
        # Generate comprehensive result
        result = generate_enhanced_result(final_state)
        
        return result
        
    except Exception as e:
        print(f"❌ Enhanced test case execution failed: {str(e)}")
        import traceback
        traceback.print_exc()
        
        return {
            "success": False,
            "error": str(e),
            "test_case_name": test_case_name,
            "execution_status": "error"
        }


def generate_enhanced_result(state: DeploymentState) -> Dict[str, Any]:
    """
    Generate comprehensive result from final state
    """
    # Basic information
    result = {
        "test_case_name": state.get("test_case_name", "Unknown"),
        "expected_result": state.get("expected_result", ""),
        "device": state.get("device", "Unknown"),
    }
    
    # Execution metrics
    total_steps = state.get("total_steps", 0)
    completed_steps = state.get("completed_steps", 0)
    current_step = state.get("current_step", 0)
    
    result.update({
        "total_steps": total_steps,
        "completed_steps": completed_steps,
        "current_step": current_step,
        "success_rate": completed_steps / total_steps if total_steps > 0 else 0,
        "execution_status": state.get("execution_status", "unknown")
    })
    
    # Overall success determination
    success_threshold = 0.8  # 80% of steps must complete successfully
    result["success"] = (
        result["success_rate"] >= success_threshold and 
        state.get("execution_status") in ["completed", "running"]
    )
    
    # Step details
    task_steps = state.get("task_steps", [])
    result["steps_executed"] = task_steps[:completed_steps] if task_steps else []
    result["all_steps"] = task_steps
    
    # Execution history
    result["execution_history"] = state.get("history", [])
    
    # Completion report
    completion_report = state.get("completion_report", {})
    if completion_report:
        result["completion_report"] = completion_report
    
    # Final verification
    final_verification = state.get("final_verification", {})
    if final_verification:
        result["final_verification"] = final_verification
    
    # Error information
    error_records = [r for r in result["execution_history"] if r.get("status") == "error"]
    if error_records:
        result["errors"] = error_records
        if not result["success"]:
            result["error_message"] = f"Encountered {len(error_records)} errors during execution"
    
    # Performance metrics
    history = result["execution_history"]
    timestamps = [r.get("timestamp") for r in history if r.get("timestamp")]
    
    if len(timestamps) >= 2:
        from datetime import datetime
        try:
            start_time = datetime.fromisoformat(timestamps[0])
            end_time = datetime.fromisoformat(timestamps[-1])
            duration = (end_time - start_time).total_seconds()
            result["execution_duration"] = duration
            result["average_step_time"] = duration / max(completed_steps, 1)
        except:
            pass
    
    return result


# Backward compatibility function
def run_multi_step_test_case(
    test_case_name: str,
    test_case_description: str,
    expected_result: str,
    device: str = "emulator-5554"
) -> Dict[str, Any]:
    """
    Backward compatibility wrapper for the enhanced system
    """
    return run_enhanced_multi_step_test_case(
        test_case_name, test_case_description, expected_result, device
    )


if __name__ == "__main__":
    # Example usage
    test_result = run_enhanced_multi_step_test_case(
        test_case_name="示例测试用例",
        test_case_description="""
        用例步骤：
        1. 点击我tab
        2. 点击编辑资料
        3. 点击性别
        4. 选择想要修改的性别/生日
        """,
        expected_result="成功修改个人资料信息",
        device="4f7d025f"
    )
    
    print("\n" + "="*60)
    print("📊 Enhanced Test Result")
    print("="*60)
    print(f"Success: {test_result.get('success', False)}")
    print(f"Steps: {test_result.get('completed_steps', 0)}/{test_result.get('total_steps', 0)}")
    print(f"Success Rate: {test_result.get('success_rate', 0):.1%}")
    print(f"Status: {test_result.get('execution_status', 'unknown')}")
    
    if test_result.get('completion_report'):
        print(f"\n📋 Completion Report Available")
    
    if test_result.get('final_verification'):
        print(f"🔍 Final Verification Available")
