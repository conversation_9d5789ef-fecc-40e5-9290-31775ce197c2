#!/usr/bin/env python3
"""
测试智能重试机制

验证死循环问题是否已修复，以及步骤验证是否更加智能
"""

from deployment import run_multi_step_test_case


def test_smart_retry_mechanism():
    """
    测试智能重试机制
    """
    print("🧪 测试智能重试机制")
    print("="*60)
    
    print("主要改进:")
    print("✅ 修复死循环问题 - 失败步骤不会无限重试")
    print("✅ 智能步骤验证 - 基于操作成功性判断")
    print("✅ 灵活处理策略 - 失败时可跳过继续执行")
    print("✅ 简化AI验证 - 减少误判")
    print()
    
    # 使用您的测试用例
    test_case_name = "修改性别、生日"
    
    test_case_description = """
    用例步骤：
    1. 点击我tab
    2. 点击编辑资料
    3. 点击性别
    4. 选择想要修改的性别/生日
    """
    
    expected_result = "成功修改我的性别/生日，我的资料页下拉刷新可看到最新消息，其他玩伴查看我的个人资料页可看到最新信息"
    
    print(f"🚀 执行测试用例: {test_case_name}")
    print(f"📱 使用设备: 4f7d025f")
    print("-" * 60)
    
    try:
        result = run_multi_step_test_case(
            test_case_name=test_case_name,
            test_case_description=test_case_description,
            expected_result=expected_result,
            device="4f7d025f"
        )
        
        print("\n" + "="*60)
        print("📊 智能重试测试结果")
        print("="*60)
        
        # 分析执行结果
        total_steps = result.get('total_steps', 0)
        completed_steps = result.get('completed_steps', 0)
        execution_status = result.get('execution_status', 'unknown')
        
        print(f"执行状态: {execution_status}")
        print(f"步骤进度: {completed_steps}/{total_steps}")
        print(f"整体结果: {'✅ 成功' if result.get('success') else '⚠️ 部分成功' if completed_steps > 0 else '❌ 失败'}")
        
        # 检查是否有死循环
        execution_history = result.get('execution_history', [])
        retry_records = [r for r in execution_history if 'retry' in str(r).lower()]
        
        if len(retry_records) > 10:
            print(f"⚠️ 检测到大量重试记录 ({len(retry_records)} 条)，可能仍有死循环问题")
        else:
            print(f"✅ 重试机制正常，共 {len(retry_records)} 次重试")
        
        # 分析步骤执行情况
        if result.get('steps_executed'):
            print(f"\n📝 步骤执行详情:")
            for i, step in enumerate(result['steps_executed'], 1):
                if i <= completed_steps:
                    status = "✅ 成功"
                elif i == completed_steps + 1:
                    status = "⚠️ 可能失败但已跳过"
                else:
                    status = "⏭️ 未执行"
                print(f"  步骤 {i}: {status} - {step}")
        
        # 检查验证记录
        verification_records = [r for r in execution_history if r.get('action') == 'step_verification']
        print(f"\n🔍 步骤验证记录 ({len(verification_records)} 条):")
        
        for i, record in enumerate(verification_records, 1):
            step_num = record.get('step', 'Unknown')
            verification_result = record.get('verification_result', 'Unknown')
            status = "✅" if "success" in verification_result.lower() else "❌"
            
            print(f"  验证 {i}: 步骤{step_num} {status}")
            print(f"    结果: {verification_result}")
        
        # 检查是否有明显的问题
        print(f"\n🔧 问题诊断:")
        
        if completed_steps == 0:
            print("  ❌ 第一步就失败，可能是设备连接或界面识别问题")
        elif completed_steps == total_steps:
            print("  ✅ 所有步骤都成功执行")
        else:
            print(f"  ⚠️ 在第 {completed_steps + 1} 步遇到问题，但系统智能跳过继续执行")
            print("  这是新的智能处理机制，避免了死循环")
        
        # 检查具体的改进效果
        print(f"\n💡 改进效果验证:")
        
        # 1. 检查是否还有死循环
        if len(retry_records) <= 6:  # 最多2次重试 * 3步 = 6次
            print("  ✅ 死循环问题已修复")
        else:
            print("  ❌ 可能仍有死循环问题")
        
        # 2. 检查步骤验证是否更智能
        false_failures = 0
        for record in verification_records:
            verification_result = record.get('verification_result', '')
            if 'failed' in verification_result.lower() and '没有变化' in verification_result:
                false_failures += 1
        
        if false_failures <= 1:
            print("  ✅ 步骤验证更加智能，减少了误判")
        else:
            print(f"  ⚠️ 仍有 {false_failures} 次可能的误判")
        
        # 3. 检查是否能继续执行
        if completed_steps > 0:
            print("  ✅ 系统能够智能处理失败步骤，继续执行")
        else:
            print("  ❌ 系统在第一步就停止了")
        
        return result
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        print("如果这是因为死循环导致的中断，说明问题仍未完全解决")
        return None
        
    except Exception as e:
        print(f"\n❌ 测试执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """
    主函数
    """
    print("🛠️ 智能重试机制测试工具")
    print("="*60)
    
    print("此测试用于验证以下问题是否已修复:")
    print("❌ 一步不成功就会一直死循环")
    print("❌ 明明找到了编辑图标但验证说失败了")
    print("❌ 步骤执行混乱（验证步骤2时却执行了步骤3）")
    print()
    
    confirm = input("是否开始测试? (y/n): ").strip().lower()
    if confirm != 'y':
        print("测试取消")
        return
    
    result = test_smart_retry_mechanism()
    
    print("\n" + "="*60)
    print("🎯 修复验证总结")
    print("="*60)
    
    if result is None:
        print("❌ 测试未能完成")
    else:
        print("✅ 测试完成，主要改进:")
        print("  1. 🔄 智能重试机制 - 不会无限循环")
        print("  2. 🧠 简化步骤验证 - 减少误判")
        print("  3. ⏭️ 灵活失败处理 - 可跳过失败步骤继续执行")
        print("  4. 📊 详细执行记录 - 便于问题诊断")
        
        if result.get('success'):
            print("\n🎉 测试用例完全成功！")
        elif result.get('completed_steps', 0) > 0:
            print(f"\n⚠️ 测试部分成功 ({result.get('completed_steps')}/{result.get('total_steps')} 步)")
            print("   这比之前的死循环要好很多")
        else:
            print("\n❌ 测试仍未成功，可能需要进一步调整")
    
    print("\n💡 使用建议:")
    print("  - 系统现在更加智能，会自动处理失败步骤")
    print("  - 如果某步骤确实无法执行，系统会跳过继续")
    print("  - 可以通过执行历史分析具体的问题原因")
    
    print("\n感谢使用智能重试测试工具！")


if __name__ == "__main__":
    main()
