from typing import Any

from langchain.prompts import ChatPromptTemplate
from langchain.schema.messages import HumanMessage, SystemMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_openai import AzureChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from openai import OpenAI

from data.State import DeploymentState
from tool.screen_content import *
from tool.utils import parse_action_with_coordinates

os.environ["LANGCHAIN_TRACING_V2"] = config.LANGCHAIN_TRACING_V2
os.environ["LANGCHAIN_ENDPOINT"] = config.LANGCHAIN_ENDPOINT
os.environ["LANGCHAIN_API_KEY"] = config.LANGCHAIN_API_KEY
os.environ["LANGCHAIN_PROJECT"] = "DeploymentExecution"

model = AzureChatOpenAI(
    azure_endpoint=config.LLM_BASE_URL,
    openai_api_key=config.LLM_API_KEY,
    deployment_name=config.LLM_MODEL,
    request_timeout=config.LLM_REQUEST_TIMEOUT,
    openai_api_version="2024-02-15-preview",
    max_retries=config.LLM_MAX_RETRIES,
    max_tokens=config.LLM_MAX_TOKEN,
)


def capture_and_parse_screen(state: DeploymentState) -> DeploymentState:
    """
    Capture current screen and parse elements, update state

    Args:
        state: Deployment state

    Returns:
        Updated deployment state
    """
    try:
        # 1. Take screenshot
        screenshot_path = take_screenshot.invoke(
            {
                "device": state["device"],
                "app_name": "deployment",
                "step": state["current_step"],
            }
        )

        if not screenshot_path or not os.path.exists(screenshot_path):
            print("❌ Screenshot failed")
            return state

        state["current_page"]["screenshot"] = screenshot_path
        return state

    except Exception as e:
        print(f"❌ Error capturing and parsing screen: {str(e)}")
        return state


def get_location(state: DeploymentState) -> DeploymentState:
    """
    Get location coordinates through screenshot and model analysis, then execute operations

    Args:
        state: Execution state

    Returns:
        Updated execution state
    """
    print("🔄 Using get_location for task execution...")
    task = state["task"]

    # Create action_agent for page operation decisions
    action_agent = create_react_agent(model, [screen_action])

    # Initialize if no messages exist
    if not state["messages"]:
        state = capture_and_parse_screen(state)
        if not state["current_page"]["screenshot"]:
            state["execution_status"] = "error"
            print("Unable to capture or parse screen")
            return state

    screenshot_path = state["current_page"]["screenshot"]
    device = state["device"]

    with open(screenshot_path, "rb") as f:
        image_data = f.read()
        image_data_base64 = base64.b64encode(image_data).decode("utf-8")

    client = OpenAI(
        base_url="http://10.65.230.19:8005/v1",
        api_key="aaa"
    )

    messages = [
        {
            "role": "user",
            "content": f'''
            
            ## 角色
            你是一个安卓智能手机操作助手，请你严格依据截图判断下一步操作。
            
            ## 输入说明：
            你将根据用户提供的手机截图，判断当前界面状态，并根据任务目标，输出下一步合理的 adb 操作指令。

            ## 输出格式（务必严格遵循）
            Thought: ...
            Action: ...
            
            ## Action Space（只能使用以下动作）
            - click(start_box='<|box_start|>(x1, y1)<|box_end|>') # 点击屏幕
            - type(content='input text') # 输入文本
            - long_press(x=123, y=456) # 长按某个屏幕
            - drag(start_box='<|box_start|>(x1, y1)<|box_end|>', end_box='<|box_start|>(x3, y3)<|box_end|>') # 滑动
            - wait() # 等待
            - finished(content='xxx') # 当前任务已完成，输出总结
            
            # 注意
            - 只能使用 Action Space 中的操作，不要输出“点击按钮”、“执行跳转”等非 adb 指令
            - 点击、按压禁止使用click，必须使用tap
            - Action 必须只有一条指令，不能混合
            - 坐标必须是整数格式，内容必须合理
            - 如果无法识别界面状态，可以输出 finished(content='无法识别当前界面')
        
            # 用户任务
              f{task}
            '''
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_data_base64}"
                    }
                }
            ]
        }
    ]

    # Call OpenAI model directly
    try:
        chat_completion = client.chat.completions.create(
            model="ByteDance-Seed/UI-TARS-1.5-7B",
            messages=messages,
            top_p=None,
            temperature=0.1,
            max_tokens=400,
            stream=True,
            seed=None,
            stop=None,
            frequency_penalty=None,
            presence_penalty=None
        )

        response = ""
        for message in chat_completion:
            if message.choices[0].delta.content:
                response += message.choices[0].delta.content

        print(f"Model response: {response}")

        # Parse action with coordinates
        action_result = parse_action_with_coordinates(response)
        print(f"Parsed action: {action_result}")

        if action_result:
            # Handle different action types
            action_type = action_result["action"]

            # Check if task is finished
            if action_type == "finished":
                state["completed"] = True
                state["execution_status"] = "completed"
                print(f"✓ Task completed: {action_result.get('content', '')}")

                state["current_step"] += 1
                state["history"].append(
                    {
                        "step": state["current_step"],
                        "screenshot": screenshot_path,
                        "action": "finished",
                        "model_response": response,
                        "parsed_action": action_result,
                        "status": "success",
                    }
                )
                return state

            try:
                system_message = SystemMessage(
                    content="""You are a mobile device operation assistant. Use the screen_action tool to perform the requested action."""
                )
                state["messages"].append(system_message)
                user_message = HumanMessage(
                    content=f"Please use screen_action tool to perform {action_result} action on device {device}"
                )
                state["messages"].append(user_message)

                agent_result = action_agent.invoke({"messages": state["messages"]})

                # Extract the result from agent response
                if agent_result and "messages" in agent_result:
                    final_message = agent_result["messages"][-1]
                    screen_result = {"status": "success", "agent_response": final_message.content}
                else:
                    screen_result = {"status": "error", "message": "No response from action agent"}

                print(f"Action agent result: {screen_result}")

            except Exception as e:
                screen_result = {"status": "error", "message": f"Action agent execution failed: {str(e)}"}
                print(f"Action agent error: {screen_result}")

            # Update execution status
            state["current_step"] += 1
            state["history"].append(
                {
                    "step": state["current_step"],
                    "screenshot": screenshot_path,
                    "action": "get_location",
                    "model_response": response,
                    "parsed_action": action_result,
                    "screen_result": screen_result,
                    "status": "success",
                }
            )

            state["execution_status"] = "success"
            print(f"✓ get_location execution successful: {action_result}")

        else:
            error_msg = "Failed to parse action from model response"
            print(f"❌ {error_msg}")

            state["history"].append(
                {
                    "step": state["current_step"],
                    "screenshot": screenshot_path,
                    "action": "get_location",
                    "model_response": response,
                    "status": "error",
                    "error": error_msg,
                }
            )

            state["execution_status"] = "error"

    except Exception as e:
        error_msg = f"Model call failed: {str(e)}"
        print(f"❌ {error_msg}")

        state["history"].append(
            {
                "step": state["current_step"],
                "screenshot": screenshot_path,
                "action": "get_location",
                "status": "error",
                "error": error_msg,
            }
        )

        state["execution_status"] = "error"

    return state


def run_task(task: str, device: str = "emulator-5554") -> Dict[str, Any]:
    """
    Execute a single task

    Args:
        task: User task description
        device: Device ID

    Returns:
        Execution result
    """
    print(f"🚀 Starting task execution: {task}")

    try:
        # Initialize state using create_deployment_state function
        from data.State import create_deployment_state

        state = create_deployment_state(
            task=task,
            device=device,
            max_retries=3,
        )

        # Execute task using LangGraph workflow
        workflow = build_workflow()
        app = workflow.compile()
        result = app.invoke(state)

        # Display final screenshot if execution was successful
        if (
                result["execution_status"] == "success"
                and result["current_page"]["screenshot"]
        ):
            try:
                from PIL import Image

                img = Image.open(result["current_page"]["screenshot"])
                img.show()
            except Exception as e:
                print(f"Unable to display final screenshot: {str(e)}")

        return {
            "status": result["execution_status"],
            "message": "Task execution completed",
            "steps_completed": result["current_step"],
            "total_steps": result["total_steps"],
        }

    except Exception as e:
        print(f"❌ Error executing task: {str(e)}")
        return {
            "status": "error",
            "message": f"Error executing task: {str(e)}",
            "error": str(e),
        }


def capture_screen_node(state: DeploymentState) -> DeploymentState:
    print("📸 Capturing and parsing current screen...")

    state_dict = dict(state)
    updated_state = capture_and_parse_screen(state_dict)

    # Update state
    for key, value in updated_state.items():
        if key in state:
            state[key] = value

    if not state["current_page"]["screenshot"]:
        print("❌ Unable to capture screen, will proceed to React mode")
    else:
        print("✓ Screen captured successfully")

    return state


def fallback_node(state: DeploymentState) -> DeploymentState:
    """
    Use get_location for task execution
    """
    # Call get_location function
    state = get_location(state)

    # Mark task as completed
    state["completed"] = True

    return state


# Routing functions
def is_task_completed(state: DeploymentState) -> str:
    """
    Check if task is completed
    """
    if state["completed"]:
        return "end"
    return "continue"


# Build simplified workflow
def build_workflow() -> StateGraph:
    """
    Build simplified workflow state graph
    """
    workflow = StateGraph(DeploymentState)

    # Add only essential nodes
    workflow.add_node("capture_screen", capture_screen_node)
    workflow.add_node("fallback", fallback_node)
    workflow.add_node("check_completion", check_task_completion)

    # Define simplified edges
    workflow.set_entry_point("capture_screen")

    # Direct flow: capture_screen -> fallback -> check_completion
    workflow.add_edge("capture_screen", "fallback")
    workflow.add_edge("fallback", "check_completion")

    # Routing after task completion check
    workflow.add_conditional_edges(
        "check_completion",
        is_task_completed,
        {"end": END, "continue": "capture_screen"},
    )

    return workflow


def check_task_completion(state: DeploymentState) -> DeploymentState:
    """
    Determine if task is completed

    Args:
        state: Execution state

    Returns:
        Updated execution state with task completion status
    """
    # Skip judgment if too few steps
    if state["current_step"] < 2:
        return state

    print("🔍 Evaluating if task is completed...")

    # Get task description
    task = state["task"]

    # Step 1: Generate task completion criteria
    completion_prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                "You are an assistant that will help analyze task completion criteria. Please carefully read the following user task:",
            ),
            (
                "human",
                f"The user's task is: {task}\nPlease describe clear, checkable task completion criteria. For example: 'When certain elements or states appear on the page, it indicates the task is complete.'",
            ),
        ]
    )

    completion_chain = completion_prompt | model | StrOutputParser()
    completion_criteria = completion_chain.invoke({})

    # Collect recent screenshots
    recent_screenshots = []
    for step in state["history"][-3:]:
        if "screenshot" in step and step["screenshot"]:
            recent_screenshots.append(step["screenshot"])

    if not recent_screenshots:
        if state["current_page"]["screenshot"]:
            recent_screenshots.append(state["current_page"]["screenshot"])

    if not recent_screenshots:
        print("⚠️ No screenshots available, cannot determine if task is complete")
        return state

    # Build image messages
    image_messages = []
    for idx, img_path in enumerate(recent_screenshots, start=1):
        if os.path.exists(img_path):
            with open(img_path, "rb") as f:
                img_data = base64.b64encode(f.read()).decode("utf-8")
            image_messages.append(
                HumanMessage(
                    content=[
                        {"type": "text", "text": f"Here is data for screenshot {idx}:"},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{img_data}"},
                        },
                    ]
                )
            )

    # Step 2: Determine if task is complete
    judgement_prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                "You are a page assessment assistant that will determine if a task is complete based on completion criteria and current page screenshots. Please only respond with 'yes' or 'no'.",
            ),
            (
                "human",
                f"The completion criteria is: {completion_criteria}\n"
                f"Based on the following screenshots, determine if the task is complete. Note that if screenshots are identical, it may indicate the task cannot proceed, so respond with 'yes' to end the program.",
            ),
        ]
    )

    # Combine all messages
    all_messages = list(judgement_prompt.messages) + image_messages

    # Call LLM for judgment
    judgement_response = model.invoke(all_messages)
    judgement_answer = judgement_response.content.strip()

    # Update task completion status
    if "yes" in judgement_answer.lower() or "complete" in judgement_answer.lower():
        state["completed"] = True
        state["execution_status"] = "completed"
        print(f"✓ Task completed: {judgement_answer}")
    else:
        state["completed"] = False
        print(f"⚠️ Task not completed: {judgement_answer}")

    # Add to history
    state["history"].append(
        {
            "step": state["current_step"],
            "action": "task_completion_check",
            "completion_criteria": completion_criteria,
            "judgement": judgement_answer,
            "status": "success",
            "completed": state["completed"],
        }
    )

    return state
