import re
from typing import Any, List

from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.schema.messages import HumanMessage, SystemMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_openai import AzureChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from openai import OpenAI

from data.State import DeploymentState
from tool.screen_content import *
from tool.utils import parse_action_with_coordinates

os.environ["LANGCHAIN_TRACING_V2"] = config.LANGCHAIN_TRACING_V2
os.environ["LANGCHAIN_ENDPOINT"] = config.LANGCHAIN_ENDPOINT
os.environ["LANGCHAIN_API_KEY"] = config.LANGCHAIN_API_KEY
os.environ["LANGCHAIN_PROJECT"] = "DeploymentExecution"

model = AzureChatOpenAI(
    azure_endpoint=config.LLM_BASE_URL,
    openai_api_key=config.LLM_API_KEY,
    deployment_name=config.LLM_MODEL,
    request_timeout=config.LLM_REQUEST_TIMEOUT,
    openai_api_version="2024-02-15-preview",
    max_retries=config.LLM_MAX_RETRIES,
    max_tokens=config.LLM_MAX_TOKEN,
)


def parse_test_case_steps(task: str) -> List[str]:
    """
    Parse test case description into individual steps

    Args:
        task: Test case description

    Returns:
        List of individual steps
    """
    print(f"🔍 Parsing test case steps from: {task}")

    # Use LLM to parse test case steps
    parse_prompt = ChatPromptTemplate.from_messages([
        ("system", """你是一个测试用例解析助手。请将用户提供的测试用例描述解析为具体的执行步骤。

输出格式要求：
- 每个步骤一行
- 步骤要具体明确，可执行
- 按执行顺序排列
- 不要包含编号，只输出步骤内容

示例：
输入：修改性别、生日。步骤：1.点击我tab 2.点击编辑资料 3.点击性别 4.选择想要修改的性别/生日
输出：
点击我tab
点击编辑资料
点击性别
选择想要修改的性别/生日"""),
        ("human", f"请解析以下测试用例的执行步骤：\n{task}")
    ])

    try:
        parse_chain = parse_prompt | model | StrOutputParser()
        response = parse_chain.invoke({})

        # Parse steps from response
        steps = []
        for line in response.strip().split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                # Remove step numbers if present
                step = re.sub(r'^\d+[\.\)]\s*', '', line)
                if step:
                    steps.append(step)

        print(f"✓ Parsed {len(steps)} steps: {steps}")
        return steps

    except Exception as e:
        print(f"❌ Error parsing test case steps: {str(e)}")
        # Fallback: try to extract steps from original task
        steps = []
        if "步骤：" in task:
            steps_text = task.split("步骤：")[1].split("期望结果：")[0] if "期望结果：" in task else task.split("步骤：")[1]
            for line in steps_text.split():
                if line.strip():
                    step = re.sub(r'^\d+[\.\)]\s*', '', line.strip())
                    if step:
                        steps.append(step)

        if not steps:
            steps = [task]  # Use original task as single step

        return steps


def capture_and_parse_screen(state: DeploymentState) -> DeploymentState:
    """
    Capture current screen and parse elements, update state

    Args:
        state: Deployment state

    Returns:
        Updated deployment state
    """
    try:
        # 1. Take screenshot
        screenshot_path = take_screenshot.invoke(
            {
                "device": state["device"],
                "app_name": "deployment",
                "step": state["current_step"],
            }
        )

        if not screenshot_path or not os.path.exists(screenshot_path):
            print("❌ Screenshot failed")
            return state

        state["current_page"]["screenshot"] = screenshot_path
        return state

    except Exception as e:
        print(f"❌ Error capturing and parsing screen: {str(e)}")
        return state


def get_location(state: DeploymentState) -> DeploymentState:
    """
    Get location coordinates through screenshot and model analysis, then execute operations

    Args:
        state: Execution state

    Returns:
        Updated execution state
    """
    print("🔄 Using get_location for task execution...")
    task = state["task"]

    # Create action_agent for page operation decisions
    action_agent = create_react_agent(model, [screen_action])

    # Initialize if no messages exist
    if not state["messages"]:
        state = capture_and_parse_screen(state)
        if not state["current_page"]["screenshot"]:
            state["execution_status"] = "error"
            print("Unable to capture or parse screen")
            return state

    screenshot_path = state["current_page"]["screenshot"]
    device = state["device"]

    with open(screenshot_path, "rb") as f:
        image_data = f.read()
        image_data_base64 = base64.b64encode(image_data).decode("utf-8")

    client = OpenAI(
        base_url="http://10.65.230.19:8005/v1",
        api_key="aaa"
    )

    messages = [
        {
            "role": "user",
            "content": f'''
            
            ## 角色
            你是一个安卓智能手机操作助手，请你严格依据截图判断下一步操作。
            
            ## 输入说明：
            你将根据用户提供的手机截图，判断当前界面状态，并根据任务目标，输出下一步合理的 adb 操作指令。

            ## 输出格式（务必严格遵循）
            Thought: ...
            Action: ...
            
            ## Action Space（只能使用以下动作）
            - click(start_box='<|box_start|>(x1, y1)<|box_end|>') # 点击屏幕
            - type(content='input text') # 输入文本
            - long_press(x=123, y=456) # 长按某个屏幕
            - drag(start_box='<|box_start|>(x1, y1)<|box_end|>', end_box='<|box_start|>(x3, y3)<|box_end|>') # 滑动
            - wait() # 等待
            - finished(content='xxx') # 当前任务已完成，输出总结
            
            # 注意
            - 只能使用 Action Space 中的操作，不要输出“点击按钮”、“执行跳转”等非 adb 指令
            - 点击、按压禁止使用click，必须使用tap
            - Action 必须只有一条指令，不能混合
            - 坐标必须是整数格式，内容必须合理
            - 如果无法识别界面状态，可以输出 finished(content='无法识别当前界面')
        
            # 用户任务
              f{task}
            '''
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_data_base64}"
                    }
                }
            ]
        }
    ]

    # Call OpenAI model directly
    try:
        chat_completion = client.chat.completions.create(
            model="ByteDance-Seed/UI-TARS-1.5-7B",
            messages=messages,
            top_p=None,
            temperature=0.1,
            max_tokens=400,
            stream=True,
            seed=None,
            stop=None,
            frequency_penalty=None,
            presence_penalty=None
        )

        response = ""
        for message in chat_completion:
            if message.choices[0].delta.content:
                response += message.choices[0].delta.content

        print(f"Model response: {response}")

        # Parse action with coordinates
        action_result = parse_action_with_coordinates(response)
        print(f"Parsed action: {action_result}")

        if action_result:
            # Handle different action types
            action_type = action_result["action"]

            # Check if task is finished
            if action_type == "finished":
                state["completed"] = True
                state["execution_status"] = "completed"
                print(f"✓ Task completed: {action_result.get('content', '')}")

                state["current_step"] += 1
                state["history"].append(
                    {
                        "step": state["current_step"],
                        "screenshot": screenshot_path,
                        "action": "finished",
                        "model_response": response,
                        "parsed_action": action_result,
                        "status": "success",
                    }
                )
                return state

            try:
                system_message = SystemMessage(
                    content="""You are a mobile device operation assistant. Use the screen_action tool to perform the requested action."""
                )
                state["messages"].append(system_message)
                user_message = HumanMessage(
                    content=f"Please use screen_action tool to perform {action_result} action on device {device}"
                )
                state["messages"].append(user_message)

                agent_result = action_agent.invoke({"messages": state["messages"]})

                # Extract the result from agent response
                if agent_result and "messages" in agent_result:
                    final_message = agent_result["messages"][-1]
                    screen_result = {"status": "success", "agent_response": final_message.content}
                else:
                    screen_result = {"status": "error", "message": "No response from action agent"}

                print(f"Action agent result: {screen_result}")

            except Exception as e:
                screen_result = {"status": "error", "message": f"Action agent execution failed: {str(e)}"}
                print(f"Action agent error: {screen_result}")

            # Update execution status
            state["current_step"] += 1
            state["history"].append(
                {
                    "step": state["current_step"],
                    "screenshot": screenshot_path,
                    "action": "get_location",
                    "model_response": response,
                    "parsed_action": action_result,
                    "screen_result": screen_result,
                    "status": "success",
                }
            )

            state["execution_status"] = "success"
            print(f"✓ get_location execution successful: {action_result}")

        else:
            error_msg = "Failed to parse action from model response"
            print(f"❌ {error_msg}")

            state["history"].append(
                {
                    "step": state["current_step"],
                    "screenshot": screenshot_path,
                    "action": "get_location",
                    "model_response": response,
                    "status": "error",
                    "error": error_msg,
                }
            )

            state["execution_status"] = "error"

    except Exception as e:
        error_msg = f"Model call failed: {str(e)}"
        print(f"❌ {error_msg}")

        state["history"].append(
            {
                "step": state["current_step"],
                "screenshot": screenshot_path,
                "action": "get_location",
                "status": "error",
                "error": error_msg,
            }
        )

        state["execution_status"] = "error"

    return state


def run_multi_step_test_case(
    test_case_name: str,
    test_case_description: str,
    expected_result: str = "",
    device: str = "4f7d025f"
) -> Dict[str, Any]:
    """
    Execute a multi-step test case

    Args:
        test_case_name: Name of the test case
        test_case_description: Description including steps
        expected_result: Expected result description
        device: Device ID

    Returns:
        Execution result with detailed step information
    """
    print(f"🚀 Starting multi-step test case: {test_case_name}")
    print(f"📝 Description: {test_case_description}")
    if expected_result:
        print(f"🎯 Expected result: {expected_result}")

    try:
        # Initialize state for multi-step execution
        from data.State import create_deployment_state

        # Combine test case info into task description
        full_task = f"测试用例：{test_case_name}\n描述：{test_case_description}"
        if expected_result:
            full_task += f"\n期望结果：{expected_result}"

        state = create_deployment_state(
            task=full_task,
            device=device,
            max_retries=2,  # Allow 2 retries per step
        )

        # Execute multi-step workflow
        workflow = build_workflow()
        app = workflow.compile()
        result = app.invoke(state)

        # Prepare detailed result
        execution_result = {
            "test_case_name": test_case_name,
            "execution_status": result.get("execution_status", "unknown"),
            "total_steps": result.get("total_steps", 0),
            "completed_steps": result.get("current_step", 0),
            "steps_executed": result.get("task_steps", []),
            "execution_history": result.get("history", []),
            "final_screenshot": result.get("current_page", {}).get("screenshot"),
            "success": result.get("execution_status") == "success"
        }

        # Print execution summary
        print(f"\n{'='*50}")
        print(f"📊 Test Case Execution Summary")
        print(f"{'='*50}")
        print(f"Test Case: {test_case_name}")
        print(f"Status: {'✅ SUCCESS' if execution_result['success'] else '❌ FAILED'}")
        print(f"Steps: {execution_result['completed_steps']}/{execution_result['total_steps']}")

        if execution_result['steps_executed']:
            print(f"\n📋 Executed Steps:")
            for i, step in enumerate(execution_result['steps_executed']):
                status = "✅" if i < execution_result['completed_steps'] else "❌"
                print(f"  {i+1}. {status} {step}")

        # Display final screenshot if available
        if execution_result['final_screenshot'] and execution_result['success']:
            try:
                from PIL import Image
                img = Image.open(execution_result['final_screenshot'])
                img.show()
                print(f"📸 Final screenshot displayed")
            except Exception as e:
                print(f"⚠️ Could not display final screenshot: {str(e)}")

        return execution_result

    except Exception as e:
        error_result = {
            "test_case_name": test_case_name,
            "execution_status": "error",
            "error_message": str(e),
            "success": False
        }
        print(f"❌ Test case execution failed: {str(e)}")
        return error_result


def run_task(task: str, device: str = "4f7d025f") -> Dict[str, Any]:
    """
    Execute a single task

    Args:
        task: User task description
        device: Device ID

    Returns:
        Execution result
    """
    print(f"🚀 Starting task execution: {task}")

    try:
        # Initialize state using create_deployment_state function
        from data.State import create_deployment_state

        state = create_deployment_state(
            task=task,
            device=device,
            max_retries=3,
        )

        # Execute task using LangGraph workflow
        workflow = build_workflow()
        app = workflow.compile()
        result = app.invoke(state)

        # Display final screenshot if execution was successful
        if (
                result["execution_status"] == "success"
                and result["current_page"]["screenshot"]
        ):
            try:
                from PIL import Image

                img = Image.open(result["current_page"]["screenshot"])
                img.show()
            except Exception as e:
                print(f"Unable to display final screenshot: {str(e)}")

        return {
            "status": result["execution_status"],
            "message": "Task execution completed",
            "steps_completed": result["current_step"],
            "total_steps": result["total_steps"],
        }

    except Exception as e:
        print(f"❌ Error executing task: {str(e)}")
        return {
            "status": "error",
            "message": f"Error executing task: {str(e)}",
            "error": str(e),
        }


def capture_screen_node(state: DeploymentState) -> DeploymentState:
    print("📸 Capturing and parsing current screen...")

    state_dict = dict(state)
    updated_state = capture_and_parse_screen(state_dict)

    # Update state
    for key, value in updated_state.items():
        if key in state:
            state[key] = value

    if not state["current_page"]["screenshot"]:
        print("❌ Unable to capture screen, will proceed to React mode")
    else:
        print("✓ Screen captured successfully")

    return state


def parse_test_case_node(state: DeploymentState) -> DeploymentState:
    """
    Parse test case into individual steps
    """
    print("📋 Parsing test case into steps...")

    if not state["task_steps"]:
        steps = parse_test_case_steps(state["task"])
        state["task_steps"] = steps
        state["total_steps"] = len(steps)
        print(f"✓ Parsed {len(steps)} steps for execution")

    return state


def execute_current_step_node(state: DeploymentState) -> DeploymentState:
    """
    Execute the current step of the test case
    """
    if state["current_step"] >= len(state["task_steps"]):
        print("✓ All steps completed")
        state["completed"] = True
        return state

    current_step_desc = state["task_steps"][state["current_step"]]
    print(f"🎯 Executing step {state['current_step'] + 1}/{len(state['task_steps'])}: {current_step_desc}")

    # Update task to current step for execution
    original_task = state["task"]
    state["task"] = current_step_desc

    # Execute current step
    state = get_location(state)

    # Restore original task
    state["task"] = original_task

    # Don't mark as completed here - let step verification decide
    return state


def verify_step_execution_node(state: DeploymentState) -> DeploymentState:
    """
    Verify if the current step was executed correctly
    """
    if state["current_step"] >= len(state["task_steps"]):
        return state

    current_step_desc = state["task_steps"][state["current_step"]]
    print(f"🔍 Verifying step execution: {current_step_desc}")

    # Get current screenshot for verification
    if not state["current_page"]["screenshot"]:
        state = capture_and_parse_screen(state)

    # Use LLM to verify step execution
    verify_prompt = ChatPromptTemplate.from_messages([
        ("system", """你是一个UI自动化测试验证助手。请根据当前截图判断指定的操作步骤是否已正确执行。

判断标准：
1. 点击操作：检查是否进入了预期的页面、弹出了对话框、或触发了预期的界面变化
2. 输入操作：检查输入框是否显示了输入的内容，或者是否进入了输入状态
3. 选择操作：检查是否选中了正确的选项，或者是否显示了选择结果
4. 导航操作：检查是否跳转到了正确的页面或标签
5. 界面变化：检查页面元素、布局、内容是否发生了预期的变化

特别注意：
- 如果是点击tab或按钮，检查是否高亮显示或进入对应页面
- 如果是编辑操作，检查是否进入编辑模式或显示编辑界面
- 如果是选择操作，检查选项是否被选中或显示选择状态
- 考虑操作的上下文和预期结果

输出格式：
- 如果步骤成功执行：回答 "success: [简要说明成功的证据]"
- 如果步骤执行失败：回答 "failed: [失败的原因]"
- 如果无法确定：回答 "failed: 无法确定执行结果"

示例：
- success: 成功点击"我"tab，当前页面显示个人资料界面
- failed: 点击后页面没有变化，可能点击位置不正确
- success: 成功进入编辑资料页面，显示可编辑的个人信息表单"""),
        ("human", f"请验证以下操作步骤是否已正确执行：\n\n步骤：{current_step_desc}\n\n请仔细查看当前截图，分析界面状态，判断该步骤是否成功执行。")
    ])

    try:
        # Prepare image message
        import base64
        with open(state["current_page"]["screenshot"], "rb") as f:
            image_content = f.read()
        image_data = base64.b64encode(image_content).decode("utf-8")

        image_message = HumanMessage(
            content=[
                {"type": "text", "text": f"当前截图用于验证步骤：{current_step_desc}"},
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/png;base64,{image_data}"}
                }
            ]
        )

        verify_chain = verify_prompt | model | StrOutputParser()
        verification_result = verify_chain.invoke({})

        print(f"📊 Step verification result: {verification_result}")

        # Parse verification result
        if "success" in verification_result.lower():
            print(f"✅ Step {state['current_step'] + 1} executed successfully")
            state["current_step"] += 1
            state["step_failed"] = False
            state["retry_count"] = 0  # Reset retry count on success
        else:
            print(f"❌ Step {state['current_step'] + 1} execution failed: {verification_result}")
            state["step_failed"] = True
            state["retry_count"] += 1

        # Add verification to history
        state["history"].append({
            "step": state["current_step"],
            "step_description": current_step_desc,
            "action": "step_verification",
            "verification_result": verification_result,
            "screenshot": state["current_page"]["screenshot"],
            "status": "success" if "success" in verification_result.lower() else "failed"
        })

    except Exception as e:
        print(f"❌ Error during step verification: {str(e)}")
        state["step_failed"] = True
        state["retry_count"] += 1

    return state


def fallback_node(state: DeploymentState) -> DeploymentState:
    """
    Use get_location for task execution (legacy compatibility)
    """
    # Call get_location function
    state = get_location(state)

    # Don't automatically mark as completed - let the workflow decide
    return state


# Routing functions
def is_task_completed(state: DeploymentState) -> str:
    """
    Check if task is completed
    """
    if state["completed"]:
        return "end"
    return "continue"


def should_retry_step(state: DeploymentState) -> str:
    """
    Determine if current step should be retried
    """
    if state["step_failed"] and state["retry_count"] < state["max_retries"]:
        print(f"🔄 Retrying step {state['current_step'] + 1} (attempt {state['retry_count'] + 1}/{state['max_retries']})")
        return "retry"
    elif state["step_failed"] and state["retry_count"] >= state["max_retries"]:
        print(f"❌ Step {state['current_step'] + 1} failed after {state['max_retries']} attempts")
        state["completed"] = True
        state["execution_status"] = "failed"
        return "end"
    else:
        return "continue"


def has_more_steps(state: DeploymentState) -> str:
    """
    Check if there are more steps to execute
    """
    if state["current_step"] >= len(state["task_steps"]):
        print("🎉 All test case steps completed successfully!")
        state["completed"] = True
        state["execution_status"] = "success"
        return "end"
    return "continue"


# Build multi-step test case workflow
def build_workflow() -> StateGraph:
    """
    Build multi-step test case execution workflow
    """
    workflow = StateGraph(DeploymentState)

    # Add nodes for multi-step execution
    workflow.add_node("parse_test_case", parse_test_case_node)
    workflow.add_node("capture_screen", capture_screen_node)
    workflow.add_node("execute_step", execute_current_step_node)
    workflow.add_node("verify_step", verify_step_execution_node)
    workflow.add_node("check_completion", check_task_completion)

    # Define workflow entry point
    workflow.set_entry_point("parse_test_case")

    # Define workflow edges
    workflow.add_edge("parse_test_case", "capture_screen")
    workflow.add_edge("capture_screen", "execute_step")
    workflow.add_edge("execute_step", "verify_step")

    # Conditional routing after step verification
    workflow.add_conditional_edges(
        "verify_step",
        should_retry_step,
        {
            "retry": "capture_screen",  # Retry current step
            "continue": "check_more_steps",  # Move to next step
            "end": END  # Failed after max retries
        }
    )

    # Add node to check if more steps exist
    workflow.add_node("check_more_steps", lambda state: state)
    workflow.add_conditional_edges(
        "check_more_steps",
        has_more_steps,
        {
            "continue": "capture_screen",  # Execute next step
            "end": "check_completion"  # All steps done, final verification
        }
    )

    # Final completion check
    workflow.add_conditional_edges(
        "check_completion",
        is_task_completed,
        {"end": END, "continue": "capture_screen"}
    )

    return workflow


def check_task_completion(state: DeploymentState) -> DeploymentState:
    """
    Determine if task is completed

    Args:
        state: Execution state

    Returns:
        Updated execution state with task completion status
    """
    # Skip judgment if too few steps
    if state["current_step"] < 2:
        return state

    print("🔍 Evaluating if task is completed...")

    # Get task description
    task = state["task"]

    # Step 1: Generate task completion criteria
    completion_prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                "You are an assistant that will help analyze task completion criteria. Please carefully read the following user task:",
            ),
            (
                "human",
                f"The user's task is: {task}\nPlease describe clear, checkable task completion criteria. For example: 'When certain elements or states appear on the page, it indicates the task is complete.'",
            ),
        ]
    )

    completion_chain = completion_prompt | model | StrOutputParser()
    completion_criteria = completion_chain.invoke({})

    # Collect recent screenshots
    recent_screenshots = []
    for step in state["history"][-3:]:
        if "screenshot" in step and step["screenshot"]:
            recent_screenshots.append(step["screenshot"])

    if not recent_screenshots:
        if state["current_page"]["screenshot"]:
            recent_screenshots.append(state["current_page"]["screenshot"])

    if not recent_screenshots:
        print("⚠️ No screenshots available, cannot determine if task is complete")
        return state

    # Build image messages
    image_messages = []
    for idx, img_path in enumerate(recent_screenshots, start=1):
        if os.path.exists(img_path):
            with open(img_path, "rb") as f:
                img_data = base64.b64encode(f.read()).decode("utf-8")
            image_messages.append(
                HumanMessage(
                    content=[
                        {"type": "text", "text": f"Here is data for screenshot {idx}:"},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{img_data}"},
                        },
                    ]
                )
            )

    # Step 2: Determine if task is complete
    judgement_prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                "You are a page assessment assistant that will determine if a task is complete based on completion criteria and current page screenshots. Please only respond with 'yes' or 'no'.",
            ),
            (
                "human",
                f"The completion criteria is: {completion_criteria}\n"
                f"Based on the following screenshots, determine if the task is complete. Note that if screenshots are identical, it may indicate the task cannot proceed, so respond with 'yes' to end the program.",
            ),
        ]
    )

    # Combine all messages
    all_messages = list(judgement_prompt.messages) + image_messages

    # Call LLM for judgment
    judgement_response = model.invoke(all_messages)
    judgement_answer = judgement_response.content.strip()

    # Update task completion status
    if "yes" in judgement_answer.lower() or "complete" in judgement_answer.lower():
        state["completed"] = True
        state["execution_status"] = "completed"
        print(f"✓ Task completed: {judgement_answer}")
    else:
        state["completed"] = False
        print(f"⚠️ Task not completed: {judgement_answer}")

    # Add to history
    state["history"].append(
        {
            "step": state["current_step"],
            "action": "task_completion_check",
            "completion_criteria": completion_criteria,
            "judgement": judgement_answer,
            "status": "success",
            "completed": state["completed"],
        }
    )

    return state
