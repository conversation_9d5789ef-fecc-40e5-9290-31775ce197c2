#!/usr/bin/env python3
"""
测试修复后的步骤验证功能

这个脚本用于验证修复后的多步骤执行和验证逻辑
"""

from deployment import run_multi_step_test_case
import time


def test_step_verification_fix():
    """
    测试修复后的步骤验证功能
    """
    print("🧪 测试修复后的步骤验证功能")
    print("="*60)
    
    test_case_name = "修改性别、生日"
    
    test_case_description = """
    用例步骤：
    1. 点击我tab
    2. 点击编辑资料
    3. 点击性别
    4. 选择想要修改的性别/生日
    """
    
    expected_result = """
    成功修改我的性别/生日，我的资料页下拉刷新可看到最新消息，
    其他玩伴查看我的个人资料页可看到最新信息。
    """
    
    print("🚀 开始执行测试用例...")
    print(f"📝 测试用例: {test_case_name}")
    print(f"📋 步骤数量: 4")
    print("-" * 60)
    
    # 执行测试
    result = run_multi_step_test_case(
        test_case_name=test_case_name,
        test_case_description=test_case_description,
        expected_result=expected_result,
        device="4f7d025f"  # 根据您的设备调整
    )
    
    # 详细分析结果
    print("\n" + "="*60)
    print("📊 详细执行分析")
    print("="*60)
    
    print(f"测试用例: {result.get('test_case_name', 'Unknown')}")
    print(f"执行状态: {result.get('execution_status', 'Unknown')}")
    print(f"总体结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
    
    total_steps = result.get('total_steps', 0)
    completed_steps = result.get('completed_steps', 0)
    print(f"步骤进度: {completed_steps}/{total_steps}")
    
    # 显示每个步骤的执行情况
    if result.get('steps_executed'):
        print(f"\n📝 步骤执行详情:")
        for i, step in enumerate(result['steps_executed'], 1):
            if i <= completed_steps:
                status = "✅ 成功"
            else:
                status = "❌ 失败"
            print(f"  步骤 {i}: {status} - {step}")
    
    # 显示执行历史
    execution_history = result.get('execution_history', [])
    if execution_history:
        print(f"\n📋 执行历史记录 ({len(execution_history)} 条):")
        
        verification_records = [r for r in execution_history if r.get('action') == 'step_verification']
        execution_records = [r for r in execution_history if r.get('action') != 'step_verification']
        
        print(f"\n🔍 步骤验证记录 ({len(verification_records)} 条):")
        for i, record in enumerate(verification_records, 1):
            step_num = record.get('step', 'Unknown')
            step_desc = record.get('step_description', 'Unknown')
            verification_result = record.get('verification_result', 'Unknown')
            status = record.get('status', 'Unknown')
            
            print(f"  验证 {i}:")
            print(f"    步骤: {step_num} - {step_desc}")
            print(f"    结果: {status}")
            print(f"    详情: {verification_result}")
            
            if record.get('before_screenshot'):
                print(f"    执行前截图: {record['before_screenshot']}")
            if record.get('after_screenshot'):
                print(f"    执行后截图: {record['after_screenshot']}")
            print()
        
        print(f"\n⚙️ 操作执行记录 ({len(execution_records)} 条):")
        for i, record in enumerate(execution_records, 1):
            action = record.get('action', 'Unknown')
            status = record.get('status', 'Unknown')
            step = record.get('step', 'Unknown')
            
            print(f"  操作 {i}: 步骤{step} - {action} - {status}")
            
            if 'parsed_action' in record:
                parsed_action = record['parsed_action']
                print(f"    执行动作: {parsed_action}")
            
            if 'error' in record:
                print(f"    错误信息: {record['error']}")
    
    # 问题诊断
    print(f"\n🔧 问题诊断:")
    if not result.get('success'):
        if completed_steps == 0:
            print("  ❌ 第一步就失败了，可能是设备连接或界面识别问题")
        elif completed_steps < total_steps:
            print(f"  ⚠️ 在第 {completed_steps + 1} 步失败，前面 {completed_steps} 步成功")
            print("  建议检查失败步骤的界面状态和操作逻辑")
        
        # 分析验证失败的原因
        failed_verifications = [r for r in execution_history 
                              if r.get('action') == 'step_verification' and r.get('status') == 'failed']
        if failed_verifications:
            print(f"  🔍 有 {len(failed_verifications)} 个步骤验证失败:")
            for record in failed_verifications:
                step_desc = record.get('step_description', 'Unknown')
                verification_result = record.get('verification_result', 'Unknown')
                print(f"    - {step_desc}: {verification_result}")
    else:
        print("  ✅ 所有步骤都成功执行并通过验证")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    if not result.get('success'):
        print("  1. 检查设备屏幕状态，确保应用在前台")
        print("  2. 验证操作坐标是否准确")
        print("  3. 确认界面元素是否可见和可点击")
        print("  4. 检查网络连接和应用响应速度")
        print("  5. 考虑增加操作间的等待时间")
    else:
        print("  1. 可以尝试更复杂的测试用例")
        print("  2. 考虑添加更多的验证点")
        print("  3. 可以集成到自动化测试流程中")
    
    return result


def main():
    """
    主函数
    """
    print("🤖 步骤验证修复测试工具")
    print("="*60)
    
    print("此工具用于验证修复后的多步骤执行和验证逻辑")
    print("主要改进:")
    print("  ✅ 修复了步骤验证时使用错误步骤描述的问题")
    print("  ✅ 添加了执行前后截图对比功能")
    print("  ✅ 改进了步骤验证的上下文提示")
    print("  ✅ 修复了步骤重试的逻辑")
    print()
    
    confirm = input("是否开始测试? (y/n): ").strip().lower()
    if confirm != 'y':
        print("测试取消")
        return
    
    try:
        result = test_step_verification_fix()
        
        print("\n" + "="*60)
        print("🎯 测试总结")
        print("="*60)
        
        if result.get('success'):
            print("✅ 测试成功！步骤验证功能工作正常")
        else:
            print("❌ 测试失败，但这可能是正常的")
            print("   因为实际的UI操作可能需要根据具体应用调整")
        
        print(f"\n执行步骤: {result.get('completed_steps', 0)}/{result.get('total_steps', 0)}")
        
        # 询问是否查看截图
        if result.get('execution_history'):
            view_screenshots = input("\n是否查看执行过程中的截图? (y/n): ").strip().lower()
            if view_screenshots == 'y':
                try:
                    from PIL import Image
                    import os
                    
                    screenshots = []
                    for record in result['execution_history']:
                        if record.get('after_screenshot'):
                            screenshots.append(record['after_screenshot'])
                    
                    if screenshots:
                        print(f"找到 {len(screenshots)} 张截图:")
                        for i, screenshot_path in enumerate(screenshots, 1):
                            if os.path.exists(screenshot_path):
                                print(f"  {i}. {screenshot_path}")
                                try:
                                    img = Image.open(screenshot_path)
                                    img.show()
                                    time.sleep(1)  # 避免同时打开太多图片
                                except Exception as e:
                                    print(f"     无法显示: {str(e)}")
                            else:
                                print(f"  {i}. {screenshot_path} (文件不存在)")
                    else:
                        print("没有找到截图文件")
                        
                except ImportError:
                    print("需要安装 Pillow 库来查看截图: pip install Pillow")
                except Exception as e:
                    print(f"查看截图时出错: {str(e)}")
        
    except Exception as e:
        print(f"❌ 测试执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n感谢使用步骤验证修复测试工具！")


if __name__ == "__main__":
    main()
