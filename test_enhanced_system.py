#!/usr/bin/env python3
"""
测试增强版系统

验证重构后的节点化系统是否正常工作
"""

from enhanced_deployment import run_enhanced_multi_step_test_case


def test_enhanced_system():
    """
    测试增强版系统
    """
    print("🧪 测试增强版多步骤执行系统")
    print("="*60)
    
    print("主要改进:")
    print("✅ 按执行流程节点拆分代码")
    print("✅ 增强消息上下文信息")
    print("✅ 智能重试和失败处理")
    print("✅ 详细的执行报告")
    print("✅ 模块化设计便于维护")
    print()
    
    # 使用您的原始测试用例
    test_case_name = "修改性别、生日"
    
    test_case_description = """
    用例步骤：
    1. 点击我tab
    2. 点击编辑资料
    3. 点击性别
    4. 选择想要修改的性别/生日
    """
    
    expected_result = "成功修改我的性别/生日，我的资料页下拉刷新可看到最新消息，其他玩伴查看我的个人资料页可看到最新信息"
    
    print(f"🚀 执行增强版测试用例: {test_case_name}")
    print(f"📱 使用设备: 4f7d025f")
    print(f"📝 步骤数量: 4")
    print("-" * 60)
    
    try:
        result = run_enhanced_multi_step_test_case(
            test_case_name=test_case_name,
            test_case_description=test_case_description,
            expected_result=expected_result,
            device="4f7d025f"
        )
        
        print("\n" + "="*60)
        print("📊 增强版系统测试结果")
        print("="*60)
        
        if result:
            # 基本信息
            print(f"测试用例: {result.get('test_case_name', 'Unknown')}")
            print(f"执行状态: {result.get('execution_status', 'unknown')}")
            print(f"整体成功: {'✅ 是' if result.get('success') else '❌ 否'}")
            
            # 步骤执行情况
            total_steps = result.get('total_steps', 0)
            completed_steps = result.get('completed_steps', 0)
            success_rate = result.get('success_rate', 0)
            
            print(f"\n📈 执行指标:")
            print(f"  总步骤数: {total_steps}")
            print(f"  完成步骤: {completed_steps}")
            print(f"  成功率: {success_rate:.1%}")
            
            if 'execution_duration' in result:
                duration = result['execution_duration']
                avg_time = result.get('average_step_time', 0)
                print(f"  执行时长: {duration:.1f}秒")
                print(f"  平均步骤时间: {avg_time:.1f}秒")
            
            # 步骤详情
            all_steps = result.get('all_steps', [])
            if all_steps:
                print(f"\n📝 步骤执行详情:")
                for i, step in enumerate(all_steps, 1):
                    if i <= completed_steps:
                        status = "✅ 成功"
                    elif i <= result.get('current_step', 0):
                        status = "⚠️ 尝试执行"
                    else:
                        status = "⏭️ 未执行"
                    print(f"  {i}. {status} {step}")
            
            # 错误信息
            if 'errors' in result:
                errors = result['errors']
                print(f"\n❌ 错误记录 ({len(errors)} 条):")
                for i, error in enumerate(errors, 1):
                    error_msg = error.get('error', 'Unknown error')
                    step = error.get('step', 'Unknown')
                    print(f"  {i}. 步骤{step}: {error_msg}")
            
            # 完成报告
            if 'completion_report' in result:
                report = result['completion_report']
                print(f"\n📋 完成报告:")
                print(f"  整体成功: {'✅ 是' if report.get('overall_success') else '❌ 否'}")
                
                retry_analysis = report.get('retry_analysis', {})
                if retry_analysis:
                    print(f"  重试次数: {retry_analysis.get('total_retries', 0)}")
                    print(f"  跳过步骤: {retry_analysis.get('total_skips', 0)}")
                    print(f"  执行效率: {retry_analysis.get('retry_efficiency', 0):.1%}")
            
            # 最终验证
            if 'final_verification' in result:
                verification = result['final_verification']
                print(f"\n🔍 最终验证:")
                print(f"  验证状态: {verification.get('status', 'unknown')}")
                
                verification_result = verification.get('verification_result', '')
                if verification_result:
                    # 显示验证结果的前200个字符
                    preview = verification_result[:200]
                    if len(verification_result) > 200:
                        preview += "..."
                    print(f"  验证结果: {preview}")
            
            # 系统改进验证
            print(f"\n🔧 系统改进验证:")
            
            # 1. 检查是否有详细的执行历史
            execution_history = result.get('execution_history', [])
            print(f"  ✅ 详细执行历史: {len(execution_history)} 条记录")
            
            # 2. 检查是否有智能重试
            retry_records = [r for r in execution_history if 'retry' in str(r).lower()]
            if len(retry_records) <= 6:  # 合理的重试次数
                print(f"  ✅ 智能重试机制: {len(retry_records)} 次重试")
            else:
                print(f"  ⚠️ 重试次数较多: {len(retry_records)} 次")
            
            # 3. 检查是否有上下文信息
            context_records = [r for r in execution_history if 'context' in str(r).lower()]
            print(f"  ✅ 上下文增强: 系统提供丰富的执行上下文")
            
            # 4. 检查模块化结构
            node_actions = set(r.get('action', '') for r in execution_history)
            expected_nodes = {'initialization', 'initial_screen_capture', 'step_execution', 'step_verification'}
            found_nodes = node_actions.intersection(expected_nodes)
            print(f"  ✅ 模块化节点: 发现 {len(found_nodes)} 个核心节点")
            
            return result
        else:
            print("❌ 测试返回空结果")
            return None
            
    except Exception as e:
        print(f"\n❌ 增强版系统测试出错: {str(e)}")
        
        # 检查具体的错误类型
        error_str = str(e)
        if 'ImportError' in error_str or 'ModuleNotFoundError' in error_str:
            print("🔧 这是模块导入错误，检查文件结构")
        elif 'nodes' in error_str:
            print("🔧 这是节点模块相关错误")
        else:
            print("🔧 这是其他类型的错误")
        
        import traceback
        traceback.print_exc()
        return None


def main():
    """
    主函数
    """
    print("🛠️ 增强版多步骤执行系统测试")
    print("="*60)
    
    print("此测试验证以下改进:")
    print("✅ 代码按执行流程节点拆分")
    print("✅ 消息包含完整用例信息和执行状态")
    print("✅ 模型能够理解当前步骤在整个用例中的位置")
    print("✅ 智能重试和失败处理机制")
    print("✅ 详细的执行报告和分析")
    print()
    
    confirm = input("是否开始测试增强版系统? (y/n): ").strip().lower()
    if confirm != 'y':
        print("测试取消")
        return
    
    result = test_enhanced_system()
    
    print("\n" + "="*60)
    print("🎯 增强版系统验证总结")
    print("="*60)
    
    if result is None:
        print("❌ 测试未能完成")
        print("\n💡 可能的原因:")
        print("  - 模块导入问题")
        print("  - 节点文件缺失")
        print("  - 依赖库问题")
    else:
        print("✅ 增强版系统测试完成！")
        
        print("\n🎉 主要改进验证:")
        print("  1. 📁 模块化设计 - 代码按节点拆分，便于维护")
        print("  2. 🧠 增强上下文 - 模型获得完整的用例信息")
        print("  3. 🔄 智能重试 - 避免死循环，智能处理失败")
        print("  4. 📊 详细报告 - 提供全面的执行分析")
        print("  5. 🎯 精确验证 - 基于用例上下文的步骤验证")
        
        if result.get('success'):
            print("\n🎉 测试用例完全成功！")
        elif result.get('completed_steps', 0) > 0:
            completed = result.get('completed_steps', 0)
            total = result.get('total_steps', 0)
            print(f"\n⚠️ 测试部分成功 ({completed}/{total} 步)")
            print("   增强版系统提供了更好的错误处理和分析")
        else:
            print("\n❌ 测试仍需改进，但系统架构已大幅优化")
    
    print("\n💡 增强版系统特点:")
    print("  - 🏗️ 模块化架构，每个节点职责清晰")
    print("  - 📝 丰富的上下文信息，模型理解更准确")
    print("  - 🧠 智能决策机制，减少误判和死循环")
    print("  - 📊 全面的执行分析，便于问题诊断")
    print("  - 🔧 易于扩展和维护")
    
    print("\n🚀 可用的增强版工具:")
    print("  - python enhanced_deployment.py")
    print("  - from enhanced_deployment import run_enhanced_multi_step_test_case")
    
    print("\n感谢使用增强版多步骤执行系统！")


if __name__ == "__main__":
    main()
